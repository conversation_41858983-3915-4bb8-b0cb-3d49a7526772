import 'dart:async';
import 'dart:convert';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/currency.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/page/pageHome.dart';

String formatCurrencyVND(String input) {
  final digits = input.replaceAll(RegExp(r'[^\d]'), '');
  if (digits.isEmpty) return '';
  final buffer = StringBuffer();
  int count = 0;
  for (int i = digits.length - 1; i >= 0; i--) {
    buffer.write(digits[i]);
    count++;
    if (count == 3 && i != 0) {
      buffer.write('.');
      count = 0;
    }
  }
  return buffer.toString().split('').reversed.join('');
}

class ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final formatted = formatCurrencyVND(digitsOnly);

    int selectionIndex =
        formatted.length - (digitsOnly.length - newValue.selection.end);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(
        offset: selectionIndex < 0 ? 0 : selectionIndex,
      ),
    );
  }
}

class InvoiceForm extends StatefulWidget {
  final ActivityItem? editData;
  final String formTitle;
  final VoidCallback? onSubmitSuccess;

  const InvoiceForm({
    super.key,
    this.editData,
    this.formTitle = 'Mẫu hóa đơn',
    this.onSubmitSuccess,
  });

  @override
  State<InvoiceForm> createState() => _InvoiceFormState();
}

class _InvoiceFormState extends State<InvoiceForm> {
  final _formKey = GlobalKey<FormState>();
  final _currencyController = TextEditingController();
  bool _isSubmitting = false; // Add submitting state
  bool _hasChanges = false; // 标记表单是否有未保存的更改
  Currency? selectedCurrency;
  List<Currency> currencies = [];
  // Controllers for form fields
  final _buyerNameController = TextEditingController();
  final _taxCodeController = TextEditingController();
  final _amountController =
      TextEditingController(); // This will become subtotal
  final _contentController = TextEditingController();
  final _titleController = TextEditingController();
  final _swiftCodeController = TextEditingController();

  // New controllers for enhanced invoice calculation
  final _quantityController = TextEditingController();
  final _unitPriceController = TextEditingController();
  final _subtotalController = TextEditingController();
  final _totalAmountController = TextEditingController();

  bool showSwiftCode = false;
  List<PlatformFile> _selectedFiles = [];
  // Add list to store existing attachments
  List<dynamic> _existingAttachments = [];

  // Tax rate options and selected value
  final List<double> _taxRateOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  double _selectedTaxRate = 10.0; // Default 10%

  // Timer for debouncing calculation
  Timer? _calculationTimer;

  String _initialBuyerName = '';
  String _initialTaxCode = '';
  String _initialAmount = '';
  String _initialContent = '';
  String _initialTitle = '';
  String _initialSwiftCode = '';
  Currency? _initialCurrency;
  int _initialAttachmentsCount = 0;

  // Initial values for new fields
  String _initialQuantity = '';
  String _initialUnitPrice = '';
  double _initialTaxRate = 10.0;

  @override
  void initState() {
    super.initState();

    // 为所有表单字段添加监听器
    _buyerNameController.addListener(_checkFormChanged);
    _taxCodeController.addListener(_checkFormChanged);
    _amountController.addListener(_checkFormChanged);
    _contentController.addListener(_checkFormChanged);
    _titleController.addListener(_checkFormChanged);
    _swiftCodeController.addListener(_checkFormChanged);

    // Add listeners for new fields
    _quantityController.addListener(_checkFormChanged);
    _unitPriceController.addListener(_checkFormChanged);

    // Add listeners for calculation with debouncing
    _quantityController.addListener(_debouncedCalculateSubtotal);
    _unitPriceController.addListener(_debouncedCalculateSubtotal);

    // Set default values
    _quantityController.text = '1';

    loadCurrencies().then((_) {
      if (widget.editData != null) {
        // Populate form with existing data
        _titleController.text = widget.editData!.title;
        _buyerNameController.text = widget.editData!.buyerName;
        _taxCodeController.text = widget.editData!.taxCode.toString();
        _amountController.text = widget.editData!.price;
        _contentController.text = widget.editData!.description;
        _quantityController.text = widget.editData!.quantity.toString();
        _unitPriceController.text =
            formatCurrencyVND(widget.editData!.price..toString());
        // Set currency
        selectedCurrency = currencies.firstWhere(
          (c) => c.code == widget.editData!.currency,
          orElse: () => currencies.firstWhere((c) => c.code == 'VND'),
        );
        _currencyController.text =
            '${selectedCurrency?.code} - ${selectedCurrency?.currency}';

        // Load existing attachments
        if (widget.editData!.attachments != null) {
          setState(() {
            _existingAttachments = widget.editData!.attachments!;
            _selectedFiles = widget.editData!.attachments!.map((attachment) {
              return PlatformFile(
                name: attachment.fileName,
                size: int.tryParse(attachment.fileSize) ?? 0,
                path: attachment.url,
              );
            }).toList();
          });
        }
      } else {
        // Only call _loadUserData when there's no editData
        _loadUserData();
      }

      // 保存初始表单值，用于后续检测变更
      _saveInitialValues();
    });
  }

  // 保存表单的初始值
  void _saveInitialValues() {
    _initialBuyerName = _buyerNameController.text;
    _initialTaxCode = _taxCodeController.text;
    _initialAmount = _amountController.text;
    _initialContent = _contentController.text;
    _initialTitle = _titleController.text;
    _initialSwiftCode = _swiftCodeController.text;
    _initialCurrency = selectedCurrency;
    _initialAttachmentsCount = _selectedFiles.length;

    // Save initial values for new fields
    _initialQuantity = _quantityController.text;
    _initialUnitPrice = _unitPriceController.text;
    _initialTaxRate = _selectedTaxRate;

    _hasChanges = false;
  }

  // Debounced calculation to improve performance
  void _debouncedCalculateSubtotal() {
    // Cancel previous timer if it exists
    _calculationTimer?.cancel();

    // Start new timer with 300ms delay
    _calculationTimer = Timer(const Duration(milliseconds: 300), () {
      _calculateSubtotal();
    });
  }

  // Calculate subtotal when quantity or unit price changes
  void _calculateSubtotal() {
    final quantityText = _quantityController.text.trim();
    final unitPriceText = _unitPriceController.text
        .replaceAll('.', ''); // Remove thousands separators only

    if (quantityText.isNotEmpty && unitPriceText.isNotEmpty) {
      // Parse quantity as decimal (can contain decimal point)
      final quantity = double.tryParse(quantityText) ?? 0;
      // Parse unit price as integer (after removing thousands separators)
      final unitPrice = double.tryParse(unitPriceText) ?? 0;

      // Calculate subtotal
      final subtotal = quantity * unitPrice;

      // Update subtotal field with currency formatting
      _subtotalController.text = formatCurrencyVND(subtotal.toInt().toString());
      // Also update the old amount controller for backward compatibility
      _amountController.text = formatCurrencyVND(subtotal.toInt().toString());

      // Calculate total amount
      _calculateTotalAmount();
    } else {
      // Clear all calculated fields if either input is empty
      _subtotalController.text = '';
      _amountController.text = '';
      _totalAmountController.text = '';
    }
  }

  // Calculate total amount when subtotal or tax rate changes
  void _calculateTotalAmount() {
    final subtotalText = _subtotalController.text.replaceAll('.', '');

    if (subtotalText.isNotEmpty) {
      final subtotal = double.tryParse(subtotalText) ?? 0;
      final taxAmount = subtotal * (_selectedTaxRate / 100);
      final totalAmount = subtotal + taxAmount;

      _totalAmountController.text =
          formatCurrencyVND(totalAmount.toInt().toString());
    } else {
      _totalAmountController.text = '';
    }
  }

  // 检查表单是否有变更
  void _checkFormChanged() {
    final currencyChanged = selectedCurrency?.code != _initialCurrency?.code;
    final attachmentsChanged =
        _selectedFiles.length != _initialAttachmentsCount;

    final hasChanges = _buyerNameController.text != _initialBuyerName ||
        _taxCodeController.text != _initialTaxCode ||
        _amountController.text != _initialAmount ||
        _contentController.text != _initialContent ||
        _titleController.text != _initialTitle ||
        _swiftCodeController.text != _initialSwiftCode ||
        _quantityController.text != _initialQuantity ||
        _unitPriceController.text != _initialUnitPrice ||
        _selectedTaxRate != _initialTaxRate ||
        currencyChanged ||
        attachmentsChanged;

    if (_hasChanges != hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  Future<bool> _showExitConfirmationDialog() async {
    if (!_hasChanges) return true;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận'),
        content: const Text(
            'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn thoát không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false), // Không, ở lại
            child: const Text('Không'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true), // Có, thoát
            child: const Text('Có'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  // Move _loadUserData outside as a class method
  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString('user_data');

      if (userDataString != null) {
        final userData = jsonDecode(userDataString);
        // We're not setting the buyer name automatically anymore
      }
    } catch (e) {
      print('Error loading user data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Không thể tải dữ liệu người dùng: $e')),
        );
      }
    }
  }

  Future<void> loadCurrencies() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/currencies.json');
      final List<dynamic> jsonList = json.decode(jsonString);

      setState(() {
        currencies = jsonList.map((json) => Currency.fromJson(json)).toList();
        // Set default currency to VND
        selectedCurrency = currencies.firstWhere((c) => c.code == 'VND');
        // Cập nhật giá trị mặc định cho controller
        _currencyController.text =
            '${selectedCurrency?.code} - ${selectedCurrency?.currency}';
      });
    } catch (e) {
      print('Error loading currencies: $e');
      setState(() {});
    }
  }

  Future<void> pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'doc', 'docx', 'png'],
        allowMultiple: true,
        withData: true,
      );

      if (result != null) {
        setState(() {
          _selectedFiles = result.files;
          _checkFormChanged(); // 更新变更状态
        });
      }
    } catch (e) {
      print('Error picking files: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không thể chọn file. Vui lòng thử lại.'),
        ),
      );
    }
  }

  Widget _buildFilePreview(PlatformFile file) {
    // Nếu file có URL (tức là file cũ từ server)
    if (file.path?.startsWith('http') ?? false) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.network(
              file.path!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('Error loading image: $error');
                return const Icon(
                  Icons.broken_image,
                  color: Colors.red,
                  size: 24,
                );
              },
            ),
          ),
        );
      }
    }
    // Nếu là file mới được chọn (có bytes)
    else if (file.bytes != null) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.memory(
              file.bytes!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('Error loading image: $error');
                return const Icon(
                  Icons.broken_image,
                  color: Colors.red,
                  size: 24,
                );
              },
            ),
          ),
        );
      }
    }

    // Return default icon for non-image files
    return Icon(
      file.name.toLowerCase().endsWith('.pdf')
          ? Icons.picture_as_pdf
          : file.name.toLowerCase().endsWith('.doc') ||
                  file.name.toLowerCase().endsWith('.docx')
              ? Icons.description
              : Icons.insert_drive_file,
      color: AppColors.primary,
      size: 40,
    );
  }

  Widget _buildAttachmentsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_selectedFiles.isNotEmpty) ...[
          const Text(
            'Tệp đã chọn:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ..._selectedFiles.map((file) {
            // Chuyển đổi kích thước sang MB
            final fileSizeInMB = (file.size / 1024 / 1024).toStringAsFixed(2);

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                contentPadding: EdgeInsets.zero,
                leading: _buildFilePreview(file),
                title: Text(
                  file.name,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                subtitle: Text(
                  '$fileSizeInMB MB',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.close, color: Colors.red),
                  onPressed: () {
                    setState(() {
                      _selectedFiles.remove(file);
                      // Nếu là file cũ, cũng xóa khỏi danh sách file cũ
                      if (file.path?.startsWith('http') ?? false) {
                        _existingAttachments.removeWhere(
                            (attachment) => attachment.url == file.path);
                      }
                    });
                  },
                ),
                dense: true,
              ),
            );
          }).toList(),
        ],
      ],
    );
  }

  @override
  void dispose() {
    _buyerNameController.removeListener(_checkFormChanged);
    _taxCodeController.removeListener(_checkFormChanged);
    _amountController.removeListener(_checkFormChanged);
    _contentController.removeListener(_checkFormChanged);
    _titleController.removeListener(_checkFormChanged);
    _swiftCodeController.removeListener(_checkFormChanged);

    // Remove listeners for new fields
    _quantityController.removeListener(_checkFormChanged);
    _unitPriceController.removeListener(_checkFormChanged);
    _quantityController.removeListener(_debouncedCalculateSubtotal);
    _unitPriceController.removeListener(_debouncedCalculateSubtotal);

    // Cancel calculation timer
    _calculationTimer?.cancel();

    _buyerNameController.dispose();
    _taxCodeController.dispose();
    _amountController.dispose();
    _contentController.dispose();
    _currencyController.dispose();
    _titleController.dispose(); // Dispose currency controller
    _swiftCodeController.dispose();

    // Dispose new controllers
    _quantityController.dispose();
    _unitPriceController.dispose();
    _subtotalController.dispose();
    _totalAmountController.dispose();

    super.dispose();
  }

  Future<void> _submitForm({required String status}) async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Vui lòng điền đầy đủ và đúng thông tin.')),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      final formData = FormData();

      // Add form fields based on whether we're editing or creating
      if (widget.editData != null) {
        formData.fields.addAll([
          MapEntry('title', _titleController.text),
          MapEntry('categoryId', '2'), // Invoice category
          MapEntry('currency', selectedCurrency?.code ?? 'VND'),
          MapEntry(
              'price',
              _unitPriceController.text
                  .replaceAll('.', '')), // Keep for backward compatibility
          MapEntry('description', _contentController.text),
          MapEntry('buyerName', _buyerNameController.text),
          MapEntry('taxCode', _taxCodeController.text),
          MapEntry('status', status), // Use passed status
          MapEntry('taxTypeId', '1'),
          // New enhanced invoice fields
          MapEntry('quantity', _quantityController.text.replaceAll('.', '')),

          MapEntry(
              'totalPrice', _totalAmountController.text.replaceAll('.', '')),
          if (showSwiftCode && _swiftCodeController.text.isNotEmpty)
            MapEntry('swiftCode', _swiftCodeController.text),
        ]);
      } else {
        formData.fields.addAll([
          MapEntry('title', _titleController.text),
          MapEntry('categoryId', '2'),
          MapEntry('buyerName', _buyerNameController.text),
          MapEntry('taxCode', _taxCodeController.text),
          MapEntry('currency', selectedCurrency?.code ?? 'VND'),
          MapEntry(
              'price',
              _unitPriceController.text
                  .replaceAll('.', '')), // Keep for backward compatibility
          MapEntry('description', _contentController.text),
          MapEntry('status', status), // Use passed status
          MapEntry('taxTypeId', '1'),
          // New enhanced invoice fields
          MapEntry('quantity', _quantityController.text.replaceAll('.', '')),
          MapEntry(
              'totalPrice', _totalAmountController.text.replaceAll('.', '')),
          if (showSwiftCode && _swiftCodeController.text.isNotEmpty)
            MapEntry('swiftCode', _swiftCodeController.text),
        ]);
      }

      // Add files with proper error handling
      for (var file in _selectedFiles) {
        try {
          if (file.bytes != null) {
            formData.files.add(
              MapEntry(
                'attachments[]',
                MultipartFile.fromBytes(file.bytes!, filename: file.name),
              ),
            );
          } else if (file.path != null && !file.path!.startsWith('http')) {
            formData.files.add(
              MapEntry(
                'attachments[]',
                await MultipartFile.fromFile(file.path!, filename: file.name),
              ),
            );
          }
        } catch (fileError) {
          print('Error processing file ${file.name}: $fileError');
          // Continue with other files instead of stopping
        }
      }

      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      // Configure Dio headers
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      // Determine API URL based on edit/create mode
      final String url = widget.editData != null
          ? '${ApiConfig.baseUrl}${ApiEndpoints.upDateForm}/${widget.editData!.id}'
          : '${ApiConfig.baseUrl}${ApiEndpoints.upLoadForm}';

      print('=== API REQUEST DEBUG ===');
      print('URL: $url');

      print('=== FORM DATA DEBUG ===');
      formData.fields.forEach((field) {
        print('${field.key}: ${field.value}');
      });

      // Make API request
      final response = await dio.post(
        url,
        data: formData,
        onSendProgress: (sent, total) {
          if (total != -1) {
            print(
                'Upload Progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      print('Response Status: ${response.statusCode}');
      print('Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        int formId = -1;
        if (response.data != null &&
            response.data['success'] == true &&
            response.data['data'] != null &&
            response.data['data']['id'] != null) {
          formId = response.data['data']['id'];

          if (status == 'pendingaccountant') {
            try {
              final notificationDio = Dio();
              final prefs = await SharedPreferences.getInstance();
              final token = prefs.getString('auth_token') ?? '';

              notificationDio.options.headers = {
                'Authorization': 'Bearer $token',
                'Content-Type': 'application/json',
              };

              final notificationResponse = await notificationDio.post(
                '${ApiConfig.baseUrl}${ApiEndpoints.sendNotificationToAccountant}',
                data: {'form_id': formId, 'action': 'submitted_by_submitter'},
              );

              print(
                  'Notification to accountant sent successfully: ${notificationResponse.statusCode}');
            } catch (notificationError) {
              print(
                  'Error sending notification to accountant: $notificationError');
            }
          }
        }

        if (mounted) {
          // Hide keyboard
          FocusScope.of(context).unfocus();

          // Show success dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext dialogContext) {
              return AlertDialog(
                title: const Text('Thành công'),
                content: Text(status == 'draft'
                    ? 'Lưu bản nháp thành công'
                    : 'Gửi yêu cầu thành công'),
                actions: [
                  TextButton(
                    onPressed: () {
                      // Call success callback if provided
                      widget.onSubmitSuccess?.call();
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const HomePage()),
                        (route) => false,
                      );
                    },
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      } else {
        throw Exception(
            'API trả về mã lỗi: ${response.statusCode}\nMessage: ${response.data['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('Error submitting form: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi khi gửi form: $e')),
        );
      }
    } finally {
      // Make sure state is reset in all cases
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isSubmitting,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        if (_isSubmitting) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Đang xử lý, vui lòng đợi trong giây lát...'),
              duration: Duration(seconds: 2),
            ),
          );
          return;
        }

        final shouldPop = await _showExitConfirmationDialog();
        if (shouldPop && mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const HomePage()),
            (route) => false,
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(widget.formTitle,
              style: const TextStyle(color: Colors.black)),
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.black),
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              if (_isSubmitting) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Đang xử lý, vui lòng đợi trong giây lát...'),
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }

              final shouldExit = await _showExitConfirmationDialog();
              if (shouldExit && mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const HomePage()),
                  (route) => false,
                );
              }
            },
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1),
            child: Container(
              color: Colors.grey.shade300,
              height: 1,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Nhập thông tin',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Tiêu đề thanh toán',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description_outlined),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập tiêu đề thanh toán';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _buyerNameController,
                  decoration: const InputDecoration(
                    labelText: 'Tên người mua hàng',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person_outline),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập tên người mua hàng';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _taxCodeController,
                  decoration: const InputDecoration(
                    labelText: 'Mã số thuế',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.numbers_outlined),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập mã số thuế';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<Currency>(
                  value: selectedCurrency,
                  decoration: const InputDecoration(
                    labelText: 'Loại tiền thanh toán',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.currency_exchange),
                  ),
                  menuMaxHeight: 200,
                  items: currencies.map((Currency currency) {
                    return DropdownMenuItem<Currency>(
                      value: currency,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            Container(
                              width: 30,
                              child: Text(
                                currency.symbol,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text('${currency.code} - ${currency.currency}'),
                            const SizedBox(width: 8),
                            Text(
                              '(${currency.country})',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (Currency? value) {
                    setState(() {
                      selectedCurrency = value;
                      _currencyController.text =
                          '${value?.code} - ${value?.currency}';
                      // Show SwiftCode field only for non-VND currencies
                      showSwiftCode = value?.code != 'VND';
                      // Clear SwiftCode when switching back to VND
                      if (!showSwiftCode) {
                        _swiftCodeController.clear();
                      }
                    });
                  },
                  isExpanded: true,
                ),
                const SizedBox(height: 16),
                if (showSwiftCode) ...[
                  TextFormField(
                    controller: _swiftCodeController,
                    decoration: const InputDecoration(
                      labelText: 'Swift Code',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers_outlined),
                    ),
                    keyboardType:
                        TextInputType.number, // Set keyboard type to number
                    inputFormatters: [
                      FilteringTextInputFormatter
                          .digitsOnly, // Only allow digits
                    ],
                    validator: (value) {
                      if (showSwiftCode && (value == null || value.isEmpty)) {
                        return 'Vui lòng nhập Swift Code';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                ],
                TextFormField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'Số lượng (Quantity)',
                    hintText: 'Nhập số lượng',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.inventory_outlined),
                  ),
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập số lượng';
                    }
                    final numericValue = double.tryParse(value);
                    if (numericValue == null || numericValue <= 0) {
                      return 'Số lượng phải lớn hơn 0';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _unitPriceController,
                  decoration: InputDecoration(
                    labelText: 'Đơn giá (Unit Price)',
                    hintText: 'Nhập đơn giá',
                    border: const OutlineInputBorder(),
                    prefixIcon: Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        selectedCurrency?.symbol ?? '₫',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    ThousandsSeparatorInputFormatter(),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập đơn giá';
                    }
                    final numericValue = value.replaceAll('.', '');
                    final price = int.tryParse(numericValue);
                    if (price == null || price <= 0) {
                      return 'Đơn giá phải lớn hơn 0';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Tax Rate Dropdown
                DropdownButtonFormField<double>(
                  value: _selectedTaxRate,
                  decoration: const InputDecoration(
                    labelText: 'Thuế suất (Tax Rate)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.percent_outlined),
                  ),
                  items: _taxRateOptions.map((double rate) {
                    return DropdownMenuItem<double>(
                      value: rate,
                      child: Text('${rate.toInt()}%'),
                    );
                  }).toList(),
                  onChanged: (double? value) {
                    setState(() {
                      _selectedTaxRate = value ?? 10.0;
                      _calculateTotalAmount(); // Recalculate when tax rate changes
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Vui lòng chọn thuế suất';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Subtotal Field (Read-only)
                TextFormField(
                  controller: _subtotalController,
                  decoration: InputDecoration(
                    labelText: 'Thành tiền (Subtotal)',
                    border: const OutlineInputBorder(),
                    prefixIcon: Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        selectedCurrency?.symbol ?? '₫',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                  ),
                  readOnly: true,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),

                // Total Amount Field (Read-only)
                TextFormField(
                  controller: _totalAmountController,
                  decoration: InputDecoration(
                    labelText: 'Tổng tiền (Total Amount)',
                    border: const OutlineInputBorder(),
                    prefixIcon: Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        selectedCurrency?.symbol ?? '₫',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.green[50],
                  ),
                  readOnly: true,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),

                // Keep the old amount controller hidden for backward compatibility
                Visibility(
                  visible: false,
                  child: TextFormField(
                    controller: _amountController,
                  ),
                ),
                // Enhanced Invoice Calculation Section

                const SizedBox(height: 16),
                TextFormField(
                  controller: _contentController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'Nhập nội dung hóa đơn',
                    labelText: 'Nội dung hóa đơn',
                    prefixIcon: Icon(Icons.edit_note_outlined),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập nội dung hóa đơn';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ElevatedButton.icon(
                      onPressed: pickFiles,
                      icon: const Icon(
                        Icons.attach_file,
                        color: Colors.white,
                      ),
                      label: const Text('Đính kèm tài liệu'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    _buildAttachmentsList(),
                  ],
                ),
                const SizedBox(height: 24),
                // Replace single button with two buttons in a row
                Row(
                  children: [
                    // Draft button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[300],
                          foregroundColor: Colors.black87,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting
                            ? null
                            : () => _submitForm(status: 'draft'),
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.black54),
                                ),
                              )
                            : const Text(
                                'Save Draft',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                    const SizedBox(width: 16), // Spacing between buttons
                    // Send button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting
                            ? null
                            : () => _submitForm(status: 'pendingaccountant'),
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'Send',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
