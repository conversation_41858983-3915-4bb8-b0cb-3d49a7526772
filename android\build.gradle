plugins {
    id 'com.android.application' version '8.1.0' apply false // Đ<PERSON><PERSON> bảo phiên bản Gradle Plugin phù hợp với dự án của bạn
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false// Đảm bảo phiên bản Kotlin phù hợp với dự án của bạn
    
    // Thêm dòng này cho plugin Google Services, với 'apply false'
    id 'com.google.gms.google-services' version '4.4.2' apply false // Kiểm tra phiên bản mới nhất!
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
