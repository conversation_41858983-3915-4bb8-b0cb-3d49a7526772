import 'dart:convert';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:http/http.dart' as http;
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/model/activity_item.dart';

class ApiService {
  static Future<List<ActivityItem>> getForm() async {
    try {
      // Print API URL để debug
      final url = '${ApiConfig.baseUrl}${ApiEndpoints.getForm}';
      print('Calling API: $url');
      print('Headers: ${ApiConfig.headers}');

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.headers,
      );

      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> data = responseData['data'];
          return data.map((json) => ActivityItem.fromJson(json)).toList();
        } else {
          throw Exception('API returned success: false or no data');
        }
      } else {
        throw Exception('Failed to load activities: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in getForm: $e');
      if (e is FormatException) {
        print('JSON parsing error: ${e.message}');
      }
      throw Exception('Error getting activities: $e');
    }
  }

  static Future<http.Response> submitPaymentForm(
      Map<String, dynamic> formData) async {
    try {
      // In URL và headers
      // print('API URL: ${ApiConfig.baseUrl}${ApiEndpoints.submitForm}');
      // print('Headers: ${ApiConfig.headers}');

      // In dữ liệu form được gửi lên
      print('Form Data:');
      formData.forEach((key, value) {
        print('$key: $value');
      });

      // Giả lập response thành công
      return http.Response('{"success": true}', 200);
    } catch (e) {
      throw Exception('Error submitting form: $e');
    }
  }
}
