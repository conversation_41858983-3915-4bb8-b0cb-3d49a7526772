import 'package:dio/dio.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/model/notification_item.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/model/activity_item.dart';

class NotificationService {
  static Future<List<NotificationItem>> getNotifications() async {
    try {
      final dio = Dio();

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ');
      }

      // Thiết lập header
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Gọi API lấy thông báo
      final response = await dio.get(
        '${ApiConfig.baseUrl}${ApiEndpoints.notification}',
      );

      print('Notification response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> data = responseData['data'];
          // Chuyển đổi mỗi JSON item thành đối tượng NotificationItem
          final List<NotificationItem> notifications =
              data.map((json) => NotificationItem.fromJson(json)).toList();

          print('Đã lấy ${notifications.length} thông báo');
          return notifications;
        }
      }

      // Trả về danh sách rỗng nếu không có kết quả
      return [];
    } catch (e) {
      print('Error getting notifications: $e');
      throw Exception('Không thể lấy thông báo: $e');
    }
  }

  // Đánh dấu thông báo đã đọc
  static Future<bool> markAsRead(int notificationId) async {
    try {
      final dio = Dio();

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ');
      }

      // Thiết lập header
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Gọi API đánh dấu đã đọc
      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.updateNotification}/$notificationId',
        data: {'isRead': true},
      );

      print('Mark as read response: ${response.statusCode}');
      print('Mark as read response data: ${response.data}');

      return response.statusCode == 200 && response.data['success'] == true;
    } catch (e) {
      print('Error marking notification as read: $e');
      return false;
    }
  }

  // Lấy thông tin form theo ID
  static Future<ActivityItem> getFormById(int formId) async {
    try {
      final dio = Dio();

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      print('=== GET FORM BY ID API DEBUG ===');
      print('Form ID: $formId');

      if (token.isEmpty) {
        print('Error: Token is empty');
        throw Exception('Token không hợp lệ');
      }

      // Thiết lập header
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      print('Request Headers:');
      print(dio.options.headers);

      // Log URL trước khi gọi API
      final url = '${ApiConfig.baseUrl}${ApiEndpoints.getFormById}/$formId';
      print('Request URL: $url');

      // Gọi API lấy thông tin form
      final response = await dio.get(url);

      print('Response Status Code: ${response.statusCode}');
      print('Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData['success'] == true && responseData['data'] != null) {
          print('Successfully parsed form data');
          return ActivityItem.fromJson(responseData['data']);
        }
        print('Error: API returned success: false or no data');
        throw Exception('API returned success: false or no data');
      }
      print(
          'Error: Failed to load form with status code: ${response.statusCode}');
      throw Exception('Failed to load form: ${response.statusCode}');
    } catch (e) {
      print('Error getting form: $e');
      if (e is DioException) {
        print('DioError type: ${e.type}');
        print('DioError message: ${e.message}');
        print('DioError response: ${e.response?.data}');
      }
      throw Exception('Không thể lấy thông tin form: $e');
    }
  }
}
