import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:du_an_flutter/Screen/notesListScreen.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:du_an_flutter/page/pageHomeApprover.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_picker/file_picker.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';

class NoteForm extends StatefulWidget {
  const NoteForm({super.key});

  @override
  State<NoteForm> createState() => _NoteFormState();
}

class _NoteFormState extends State<NoteForm> {
  final _formKey = GlobalKey<FormState>();
  bool _isSubmitting = false;
  bool _hasChanges = false;

  // Controllers for form fields
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _dateController = TextEditingController();

  // Selected date - default to tomorrow (future date)
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));

  // Image selection
  List<PlatformFile> _selectedFiles = [];

  // Initial values for change detection
  String _initialTitle = '';
  String _initialDescription = '';
  DateTime _initialDate = DateTime.now().add(const Duration(days: 1));
  List<PlatformFile> _initialFiles = [];

  @override
  void initState() {
    super.initState();

    // Set default date to tomorrow (future date only)
    _selectedDate = DateTime.now().add(const Duration(days: 1));
    _dateController.text = _formatDate(_selectedDate);

    // Add listeners for form change detection
    _titleController.addListener(_checkFormChanged);
    _descriptionController.addListener(_checkFormChanged);

    // Save initial values
    _saveInitialValues();
  }

  // Simple date formatter
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Format date for API
  String _formatDateForAPI(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Validate if date is in the future (tomorrow or later)
  bool _isDateInFuture(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);
    final tomorrowOnly = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    return dateOnly.isAtSameMomentAs(tomorrowOnly) ||
        dateOnly.isAfter(tomorrowOnly);
  }

  // Get minimum allowed date (tomorrow)
  DateTime _getMinimumDate() {
    return DateTime.now().add(const Duration(days: 1));
  }

  @override
  void dispose() {
    _titleController.removeListener(_checkFormChanged);
    _descriptionController.removeListener(_checkFormChanged);

    _titleController.dispose();
    _descriptionController.dispose();
    _dateController.dispose();

    super.dispose();
  }

  // Save initial form values for change detection
  void _saveInitialValues() {
    _initialTitle = _titleController.text;
    _initialDescription = _descriptionController.text;
    _initialDate = _selectedDate;
    _initialFiles = List.from(_selectedFiles);
    _hasChanges = false;
  }

  // Check if form has changes
  void _checkFormChanged() {
    final hasChanges = _titleController.text != _initialTitle ||
        _descriptionController.text != _initialDescription ||
        _selectedDate != _initialDate ||
        _selectedFiles.length != _initialFiles.length ||
        !_listsEqual(_selectedFiles, _initialFiles);

    if (_hasChanges != hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  // Helper method to compare file lists
  bool _listsEqual(List<PlatformFile> list1, List<PlatformFile> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].name != list2[i].name || list1[i].size != list2[i].size) {
        return false;
      }
    }
    return true;
  }

  // Helper method to extract organization ID from user data
  String? _extractOrganizationId(Map<String, dynamic> userData) {
    try {
      // First priority: Check currentOrganization (based on server response)
      if (userData['currentOrganization'] != null &&
          userData['currentOrganization']['id'] != null) {
        return userData['currentOrganization']['id'].toString();
      }

      // Second priority: Check user_organizations array for organizationId
      if (userData['user_organizations'] is List &&
          (userData['user_organizations'] as List).isNotEmpty) {
        var userOrg = userData['user_organizations'][0];
        if (userOrg is Map && userOrg['organizationId'] != null) {
          return userOrg['organizationId'].toString();
        }

        // Third priority: Check user_organizations[0].organization.id
        if (userOrg is Map && userOrg['organization'] is Map) {
          var orgId = userOrg['organization']['id'];
          if (orgId != null) {
            return orgId.toString();
          }
        }
      }

      // Fourth priority: Check organization field
      if (userData['organization'] != null &&
          userData['organization']['id'] != null) {
        return userData['organization']['id'].toString();
      }

      // Fifth priority: Check direct organizationId field
      if (userData['organizationId'] != null) {
        return userData['organizationId'].toString();
      }

      print('Warning: Could not extract organization ID from user data');
      print('Available keys: ${userData.keys.toList()}');
      return null;
    } catch (e) {
      print('Error extracting organization ID: $e');
      return null;
    }
  }

  // Date picker function with future date validation
  Future<void> _selectDate() async {
    final DateTime minimumDate = _getMinimumDate();
    final DateTime maxDate =
        DateTime.now().add(const Duration(days: 365 * 2)); // 2 years from now

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDate.isBefore(minimumDate) ? minimumDate : _selectedDate,
      firstDate: minimumDate, // Only allow future dates (tomorrow and beyond)
      lastDate: maxDate,
      locale: const Locale('vi', 'VN'),
      helpText: 'Chọn ngày thông báo (chỉ ngày tương lai)',
      confirmText: 'Chọn',
      cancelText: 'Hủy',
      errorFormatText: 'Định dạng ngày không hợp lệ',
      errorInvalidText: 'Ngày không hợp lệ',
      fieldLabelText: 'Nhập ngày',
      fieldHintText: 'dd/mm/yyyy',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Additional validation using helper method
      if (!_isDateInFuture(picked)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Chỉ có thể chọn ngày trong tương lai (từ ngày mai trở đi)',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: 'Đóng',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
        return; // Don't update the date if it's invalid
      }

      // Update the selected date if it's valid
      if (picked != _selectedDate) {
        setState(() {
          _selectedDate = picked;
          _dateController.text = _formatDate(picked);
          _checkFormChanged();
        });
      }
    }
  }

  // Image picker function
  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png'],
        allowMultiple: true,
        withData: true, // Required for file upload
      );

      if (result != null) {
        setState(() {
          // Add new files to existing list
          _selectedFiles = [
            ..._selectedFiles,
            ...result.files,
          ];
          _checkFormChanged();
        });
      }
    } catch (e) {
      print('Error picking files: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Không thể chọn ảnh. Vui lòng thử lại.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Build file preview widget
  Widget _buildFilePreview(PlatformFile file) {
    if (file.bytes != null) {
      final extension = file.name.split('.').last.toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(extension)) {
        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.memory(
              file.bytes!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.broken_image,
                  color: Colors.red,
                  size: 24,
                );
              },
            ),
          ),
        );
      }
    }

    // Default icon for non-image files or when preview fails
    return Icon(
      Icons.image,
      color: AppColors.primary,
      size: 40,
    );
  }

  // Build image card with preview and remove button
  Widget _buildImageCard(PlatformFile file) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // Image preview
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: file.bytes != null
                  ? Image.memory(
                      file.bytes!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[100],
                          child: Icon(
                            Icons.broken_image,
                            color: Colors.grey[400],
                            size: 24,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey[100],
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[400],
                        size: 24,
                      ),
                    ),
            ),
          ),
          // Remove button
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedFiles.remove(file);
                  _checkFormChanged();
                });
              },
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Submit form function
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng điền đầy đủ thông tin bắt buộc'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Get auth token and user data
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';
      final userDataString = prefs.getString('user_data');

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      if (userDataString == null) {
        throw Exception(
            'Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.');
      }

      // Parse user data to get userId and organizationId
      final userData = jsonDecode(userDataString);

      final userId = userData['id']?.toString();
      final organizationId = _extractOrganizationId(userData);

      if (userId == null || userId.isEmpty) {
        throw Exception(
            'Không tìm thấy ID người dùng. Vui lòng đăng nhập lại.');
      }

      if (organizationId == null || organizationId.isEmpty) {
        throw Exception(
            'Không tìm thấy thông tin tổ chức. Vui lòng đăng nhập lại.');
      }

      // Prepare form data for multipart upload
      final formData = FormData();

      // Add text fields including required userId and organizationId
      formData.fields.addAll([
        MapEntry('title', _titleController.text.trim()),
        MapEntry('description', _descriptionController.text.trim()),
        MapEntry('notiDate', _formatDateForAPI(_selectedDate)),
        MapEntry('userId', userId),
        MapEntry('organizationId', organizationId),
      ]);

      // Add image files if any
      for (var file in _selectedFiles) {
        try {
          if (file.bytes != null) {
            formData.files.add(
              MapEntry(
                'image',
                MultipartFile.fromBytes(file.bytes!, filename: file.name),
              ),
            );
          }
        } catch (fileError) {
          print('Error processing file ${file.name}: $fileError');
          // Continue with other files instead of failing completely
        }
      }

      // Configure Dio headers for multipart
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      // Use the correct API endpoint
      final String url = '${ApiConfig.baseUrl}${ApiEndpoints.createNote}';

      print('=== NOTE SUBMISSION DEBUG ===');
      print('URL: $url');
      print('Form fields:');
      formData.fields.forEach((field) {
        print('${field.key}: ${field.value}');
      });
      print('Files count: ${formData.files.length}');

      final response = await dio.post(
        url,
        data: formData,
        onSendProgress: (sent, total) {
          if (total != -1) {
            print(
                'Upload Progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      print('Response Status: ${response.statusCode}');
      print('Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (mounted) {
          // Hide keyboard
          FocusScope.of(context).unfocus();

          // Show success dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext dialogContext) {
              return AlertDialog(
                title: const Text('Thành công'),
                content: const Text('Ghi chú đã được lưu thành công'),
                actions: [
                  TextButton(
                    onPressed: () async {
                      Navigator.of(dialogContext).pop(); // Close dialog first

                      if (!mounted) return;

                      // Get user role to determine which main page to navigate to
                      final prefs = await SharedPreferences.getInstance();
                      final userDataString = prefs.getString('user_data');
                      String userRole = 'Submitter'; // Default role

                      if (userDataString != null) {
                        try {
                          final userData = jsonDecode(userDataString);
                          userRole = userData['role'] ?? 'Submitter';
                        } catch (e) {
                          print('Error parsing user role: $e');
                        }
                      }

                      if (!mounted) return;

                      // Navigate back to main app preserving bottom navigation
                      if (userRole == 'Approver') {
                        // For Approver role - notes are not in bottom nav, go to home
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HomePageApprover(),
                          ),
                          (route) => route.isFirst,
                        );
                      } else {
                        // For Submitter role - navigate to main app with notes tab selected
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const HomePage(initialTabIndex: 3),
                          ),
                          (route) => route.isFirst,
                        );
                      }
                    },
                    child: const Text('OK'),
                  ),
                ],
              );
            },
          );
        }
      } else {
        throw Exception('Lỗi server: ${response.statusCode}');
      }
    } catch (e) {
      print('Error submitting note: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi lưu ghi chú: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  // Exit confirmation dialog
  Future<bool> _showExitConfirmationDialog() async {
    if (!_hasChanges) return true;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận'),
        content: const Text(
            'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn thoát không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Không'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Có'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isSubmitting,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        if (_isSubmitting) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Đang xử lý, vui lòng đợi...'),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        final canPop = await _showExitConfirmationDialog();
        if (canPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.white,
          backgroundColor: Colors.white,
          elevation: 0,
          title: const Text(
            'Tạo ghi chú',
            style: TextStyle(
              color: Colors.black,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.shade600,
              height: 1.0,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Thông tin ghi chú',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),

                // Title field
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Tiêu đề ghi chú *',
                    hintText: 'Nhập tiêu đề cho ghi chú',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title_outlined),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Vui lòng nhập tiêu đề ghi chú';
                    }
                    if (value.trim().length < 3) {
                      return 'Tiêu đề phải có ít nhất 3 ký tự';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Date picker field with future date validation
                TextFormField(
                  controller: _dateController,
                  decoration: InputDecoration(
                    labelText: 'Ngày thông báo *',
                    hintText: 'Chọn ngày (chỉ ngày tương lai)',
                    helperText: 'Chỉ có thể chọn ngày từ ngày mai trở đi',
                    helperStyle: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.calendar_today_outlined),
                    suffixIcon: const Icon(Icons.arrow_drop_down),
                  ),
                  readOnly: true,
                  onTap: _selectDate,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Vui lòng chọn ngày thông báo';
                    }

                    // Validate that selected date is in the future using helper method
                    if (!_isDateInFuture(_selectedDate)) {
                      return 'Chỉ có thể chọn ngày trong tương lai (từ ngày mai trở đi)';
                    }

                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description field
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Nội dung ghi chú *',
                    hintText: 'Nhập nội dung chi tiết cho ghi chú',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description_outlined),
                    alignLabelWithHint: true,
                  ),
                  maxLines: 5,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Vui lòng nhập nội dung ghi chú';
                    }

                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Image attachment section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.image_outlined,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Hình ảnh đính kèm',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Add image button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: _pickFiles,
                          icon: const Icon(Icons.add_photo_alternate_outlined),
                          label: const Text('Chọn hình ảnh'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: AppColors.primary),
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                      ),

                      // Display selected images
                      if (_selectedFiles.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        const Text(
                          'Hình ảnh đã chọn:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 8),
                        // Grid of selected images
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _selectedFiles
                              .map((file) => _buildImageCard(file))
                              .toList(),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    // Clear button

                    // Save button
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: _isSubmitting ? null : _submitForm,
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'Lưu ghi chú',
                                style: TextStyle(fontSize: 16),
                              ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
