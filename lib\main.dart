import 'dart:convert';
import 'dart:io';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/L10n/l10n.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/page/pageLogin.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:du_an_flutter/page/pageHomeApprover.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:du_an_flutter/providers/locale_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:device_info_plus/device_info_plus.dart';

// --- BẮT ĐẦU PHẦN MỚI CẦN THÊM ---
// Import Firebase và Local Notifications
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Khởi tạo instance của FlutterLocalNotificationsPlugin (global/top-level)
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

// GlobalKey để truy cập Navigator (tùy chọn, hữu ích cho điều hướng từ hàm top-level)
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Hàm xử lý khi người dùng click vào thông báo cục bộ
// Hàm này phải là một hàm top-level hoặc static method của một class.
@pragma('vm:entry-point')
void onDidReceiveNotificationResponse(
    NotificationResponse notificationResponse) async {
  final String? payload = notificationResponse.payload;
  if (payload != null && payload.isNotEmpty) {
    debugPrint('Notification payload clicked: $payload');
    // TODO: Tại đây, bạn có thể xử lý dữ liệu payload để điều hướng.
    // Ví dụ:
    try {
      final Map<String, dynamic> data = jsonDecode(payload);
      if (data['screen'] == 'approval_request' &&
          navigatorKey.currentState != null) {
        // Giả sử bạn có một trang chi tiết yêu cầu phê duyệt
        // navigatorKey.currentState!.push(MaterialPageRoute(builder: (context) => ApprovalRequestDetailPage(requestId: data['request_id'])));
        print(
            'Đang cố gắng điều hướng đến trang chi tiết yêu cầu phê duyệt...');
      } else if (data['screen'] == 'new_submission' &&
          navigatorKey.currentState != null) {
        // navigatorKey.currentState!.push(MaterialPageRoute(builder: (context) => NewSubmissionPage(submissionId: data['submission_id'])));
        print('Đang cố gắng điều hướng đến trang nộp mới...');
      }
    } catch (e) {
      print('Lỗi khi parse payload JSON: $e');
    }
  }
}

// Hàm xử lý tin nhắn nền (background messages). Phải là top-level function.
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Đảm bảo Firebase được khởi tạo ngay cả khi ứng dụng bị đóng hoàn toàn
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
  print("Notification Title: ${message.notification?.title}");
  print("Notification Body: ${message.notification?.body}");
  print("Data: ${message.data}");

  // Hiển thị thông báo cục bộ khi nhận được thông báo nền
  _showLocalNotification(
    message.notification?.title ?? "Thông báo mới", // Tiêu đề mặc định
    message.notification?.body ??
        "Kiểm tra ứng dụng để biết chi tiết.", // Nội dung mặc định
    jsonEncode(message.data), // Gửi data làm payload
  );
}

// Hàm helper để hiển thị thông báo cục bộ
Future<void> _showLocalNotification(
    String? title, String? body, String? payload) async {
  const AndroidNotificationDetails androidPlatformChannelSpecifics =
      AndroidNotificationDetails(
    'your_app_channel_id',
    'Kênh Thông Báo Quan Trọng',
    channelDescription:
        'Kênh này dùng cho các thông báo quan trọng của ứng dụng.',
    importance: Importance.max,
    priority: Priority.high,
    showWhen: false,
    playSound: true,
    icon: '@mipmap/ic_launcher', // Thêm icon setting
  );
  const NotificationDetails platformChannelSpecifics =
      NotificationDetails(android: androidPlatformChannelSpecifics);
  await flutterLocalNotificationsPlugin.show(
    0, // ID của thông báo (phải là số nguyên duy nhất cho mỗi thông báo bạn muốn hiển thị)
    title,
    body,
    platformChannelSpecifics,
    payload: payload, // Dữ liệu sẽ được truyền khi thông báo được click
  );
}
// --- KẾT THÚC PHẦN MỚI CẦN THÊM ---

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");

  try {
    await Firebase.initializeApp();
    print('Firebase initialized successfully');
  } catch (e) {
    print('Error initializing Firebase: $e');
  }

  // --- BẮT ĐẦU CẤU HÌNH FIREBASE VÀ LOCAL NOTIFICATIONS TRONG MAIN ---
  // Đăng ký hàm xử lý tin nhắn nền cho Firebase Messaging
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Khởi tạo Local Notifications1
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings(
          '@mipmap/ic_launcher'); // Thay đổi từ 'app_icon' sang '@mipmap/ic_launcher'

  final DarwinInitializationSettings initializationSettingsDarwin =
      DarwinInitializationSettings(onDidReceiveLocalNotification:
          (int id, String? title, String? body, String? payload) async {
    // Xử lý thông báo cục bộ khi ứng dụng ở foreground trên iOS (dành cho iOS < 10 và một số trường hợp đặc biệt)
    // Hiện tại, FirebaseMessaging.onMessage.listen và _showLocalNotification sẽ lo việc này.
    // Bạn có thể hiển thị một AlertDialog nếu muốn thông báo foreground khác biệt trên iOS.
    print(
        'iOS Local Notification (Foreground): $title - $body, payload: $payload');
  });

  final InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsDarwin,
    macOS: initializationSettingsDarwin, // Có thể dùng chung cho macOS
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    onDidReceiveBackgroundNotificationResponse:
        onDidReceiveNotificationResponse, // Quan trọng cho Android 13+ khi click thông báo nền
  );
  // --- KẾT THÚC CẤU HÌNH FIREBASE VÀ LOCAL NOTIFICATIONS TRONG MAIN ---

  // Load token và user data
  await ApiConfig.loadToken();
  final prefs = await SharedPreferences.getInstance();
  final userStr = prefs.getString('user_data');

  // Kiểm tra role từ user data
  String? roleName;
  if (userStr != null) {
    try {
      final userData = jsonDecode(userStr);
      roleName = userData['role']?['name']; // Add null check with ?
      print('User Role: $roleName'); // Add debug log
    } catch (e) {
      print('Error parsing user data: $e');
    }
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LocaleProvider()),
      ],
      child: MyApp(
        isAuthenticated: userStr != null && ApiConfig.isAuthenticated,
        userRole: roleName,
      ),
    ),
  );
}

class MyApp extends StatefulWidget {
  // Chuyển StatelessWidget thành StatefulWidget để dùng initState
  final bool isAuthenticated;
  final String? userRole;

  const MyApp({
    Key? key,
    required this.isAuthenticated,
    this.userRole,
  }) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String? _fcmToken; // Biến để lưu FCM Token

  @override
  void initState() {
    super.initState();
    _initFirebaseMessaging(); // Khởi tạo lắng nghe Firebase Messaging
    _requestNotificationPermissions(); // Yêu cầu quyền thông báo
  }

  // Yêu cầu quyền thông báo cho cả Firebase Messaging và Local Notifications
  void _requestNotificationPermissions() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Yêu cầu quyền cho Firebase Messaging (Android 13+ và iOS)
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false, // Yêu cầu quyền đầy đủ
    );

    print('User granted Firebase permission: ${settings.authorizationStatus}');

    // Yêu cầu quyền cho Local Notifications (chủ yếu iOS)
    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            MacOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  void _initFirebaseMessaging() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    try {
      _fcmToken = await messaging.getToken();
      print('FCM Token: $_fcmToken');

      // Get device info
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      Map<String, dynamic> deviceData = {};

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceData = {
          "uniqueId": androidInfo.id,
          "manufacturer": androidInfo.manufacturer,
          "brand": androidInfo.brand,
          "model": androidInfo.model,
          "deviceId": androidInfo.board,
          "baseOS": androidInfo.version.baseOS ?? '',
          "deviceName": androidInfo.device,
          "isTablet": androidInfo.displayMetrics.widthPx > 600,
          "isEmulator": !androidInfo.isPhysicalDevice,
          "getSystemName": "Android",
          "getSystemVersion": androidInfo.version.release
        };
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceData = {
          "uniqueId": iosInfo.identifierForVendor,
          "manufacturer": "Apple",
          "brand": "Apple",
          "model": iosInfo.model,
          "deviceId": iosInfo.name,
          "baseOS": "",
          "deviceName": iosInfo.name,
          "isTablet": iosInfo.model.toLowerCase().contains("ipad"),
          "isEmulator": !iosInfo.isPhysicalDevice,
          "getSystemName": "iOS",
          "getSystemVersion": iosInfo.systemVersion
        };
      }

      // Save FCM token with device info
      if (_fcmToken != null) {
        await saveFCMToken(_fcmToken!, deviceData);
      }

      // Listen for token refresh
      messaging.onTokenRefresh.listen((newToken) async {
        print('New FCM token: $newToken');
        await saveFCMToken(newToken, deviceData);
      });

      // Xử lý thông báo khi ứng dụng đang ở foreground (mở trên màn hình)
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');

        // Khi ở foreground, Firebase không tự động hiển thị thông báo trên khay hệ thống.
        // Chúng ta dùng flutter_local_notifications để hiển thị nó.
        _showLocalNotification(
          message.notification?.title ?? "Thông báo mới (Foreground)",
          message.notification?.body ?? "Kiểm tra ứng dụng để biết chi tiết.",
          jsonEncode(message.data), // Đính kèm data làm payload
        );
      });

      // Xử lý khi người dùng nhấn vào thông báo để mở ứng dụng từ chế độ background/terminated
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('Message opened app from background/terminated state!');
        print('  Title: ${message.notification?.title}');
        print('  Body: ${message.notification?.body}');
        print('  Data: ${message.data}');

        // TODO: Điều hướng người dùng đến màn hình cụ thể dựa trên `message.data`
        // Bạn có thể sử dụng navigatorKey ở đây nếu cần điều hướng từ hàm top-level
        // hoặc đảm bảo context hợp lệ (ví dụ: dùng Navigator.of(context).push(...))
        _showNotificationDialog(context, "Mở từ Thông báo",
            "Tiêu đề: ${message.notification?.title}\nNội dung: ${message.notification?.body}\nDữ liệu: ${message.data}");
      });

      // Lắng nghe sự thay đổi của token (nếu token bị refresh)
      messaging.onTokenRefresh.listen((newToken) {
        print("FCM Token mới: $newToken");
        _fcmToken = newToken;
        setState(() {});
        // Nếu bạn có backend, hãy gửi token mới này đến backend của bạn.
      });
    } catch (e) {
      print('Error in Firebase Messaging setup: $e');
    }
  }

  // Hàm helper để hiển thị AlertDialog (để test hoặc cho mục đích debug)
  void _showNotificationDialog(
      BuildContext context, String? title, String? body) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title ?? 'Thông báo'),
          content: Text(body ?? 'Không có nội dung'),
          actions: <Widget>[
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> saveFCMToken(
      String fcmToken, Map<String, dynamic> deviceInfo) async {
    try {
      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.saveFCMToken}',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        data: {'token': fcmToken, 'description': jsonEncode(deviceInfo)},
      );

      if (response.statusCode == 200) {
        print('FCM token saved successfully');
      }
    } catch (e) {
      print('Error saving FCM token: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, provider, child) {
        return MaterialApp(
          navigatorKey: navigatorKey,
          locale: provider.locale,
          supportedLocales: L10n.all,
          localizationsDelegates: const [
            AppLocalizationsDelegate(),
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          title: 'Flutter Demo',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primary),
            useMaterial3: true,
          ),
          home: _buildHomeScreen(),
        );
      },
    );
  }

  Widget _buildHomeScreen() {
    if (!widget.isAuthenticated) {
      return const pageLogin();
    }

    switch (widget.userRole) {
      case 'Submitter':
        return HomePage();
      case 'Approver':
        return HomePageApprover();
      default:
        return pageLogin();
    }
  }
}
