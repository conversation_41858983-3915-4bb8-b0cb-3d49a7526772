import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/Screen/activities_screen.dart';
import 'dart:async';

class Listactivities extends StatefulWidget {
  final String status;
  final List<ActivityItem> activities;
  final Function? onRefresh;

  const Listactivities({
    Key? key,
    required this.status,
    required this.activities,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<Listactivities> createState() => _ListactivitiesState();
}

class _ListactivitiesState extends State<Listactivities> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _debounce;
  List<ActivityItem> filteredActivities = [];

  @override
  void initState() {
    super.initState();
    filteredActivities = widget.activities;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  void activate() {
    super.activate();
    print("ListActivities được k<PERSON>ch ho<PERSON> l<PERSON>, <PERSON>n bàn phím");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusManager.instance.primaryFocus?.unfocus();
        _searchFocusNode.unfocus();
      }
    });
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        filteredActivities = widget.activities.where((activity) {
          return activity.title.toLowerCase().contains(query.toLowerCase());
        }).toList();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
        title: Text(
          'List ${widget.status} (${widget.activities.length})',
          style: const TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          Expanded(
            child: filteredActivities.isEmpty
                ? const Center(
                    child: Text(
                      'Không có dữ liệu',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: filteredActivities.length,
                    itemBuilder: (context, index) {
                      return ActivityScreen(
                        item: filteredActivities[index],
                        onRefresh: () async {
                          // Refresh the parent's data and update the filtered list
                          await widget.onRefresh?.call();
                          _onSearchChanged(_searchController.text);
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
