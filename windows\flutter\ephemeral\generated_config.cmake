# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\Flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Du_An_Ureka\\UrekaApp" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\Flutter\\flutter"
  "PROJECT_DIR=D:\\Du_An_Ureka\\UrekaApp"
  "FLUTTER_ROOT=D:\\Flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Du_An_Ureka\\UrekaApp\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Du_An_Ureka\\UrekaApp"
  "FLUTTER_TARGET=D:\\Du_An_Ureka\\UrekaApp\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Du_An_Ureka\\UrekaApp\\.dart_tool\\package_config.json"
)
