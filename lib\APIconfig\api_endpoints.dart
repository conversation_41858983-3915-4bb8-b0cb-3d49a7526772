import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiEndpoints {
  static String get getUsers => dotenv.env['GET_USERS_URL'] ?? '';
  static String get login => dotenv.env['LOGIN'] ?? '';
  static String get getForm => dotenv.env['GET_FORM'] ?? '';
  static String get upLoadForm => dotenv.env['UPLOAD_FORM'] ?? '';
  static String get upDateForm => dotenv.env['UPDATE_FORM_ID'] ?? '';
  static String get deleteForm => dotenv.env['DELETE_FORM_ID'] ?? '';
  static String get updateFormApprove =>
      dotenv.env['UPDATE_FORM_APPROVED'] ?? '';
  static String get updateFormSubmitter =>
      dotenv.env['UPDATE_FORM_SUBMITTER'] ?? '';

  static String get saveFCMToken => dotenv.env['SAVE_FCM_TOKEN'] ?? '';
  static String get upDateProfile => dotenv.env['UPDATE_PROFILE'] ?? '';
  static String get getProfile => dotenv.env['GET_PROFILE'] ?? '';
  static String get upDateProfileImage =>
      dotenv.env['UPDATE_PROFILE_IMAGE'] ?? '';
  static String get upDateProfileBank =>
      dotenv.env['UPDATE_PROFILE_BANK'] ?? '';
  static String get deleteBank => dotenv.env['DELETE_BANK'] ?? '';
  static String get filterForm => dotenv.env['FiLTER_FORM'] ?? '';
  static String get searchBank => dotenv.env['SEARCH_BANK'] ?? '';
  static String get searchForm => dotenv.env['SEARCH_FORM'] ?? '';
  static String get notification => dotenv.env['NOTIFICATION'] ?? '';
  static String get updateNotification =>
      dotenv.env['UPDATE_NOTIFICATION'] ?? '';
  static String get getOrganizations => dotenv.env['GET_ORGANIZATIONS'] ?? '';
  static String get getFormById => dotenv.env['GET_FORM_BY_ID'] ?? '';
  static String get sendNotificationToAccountant =>
      dotenv.env['SEND_NOTIFICATION_TO_ACCOUNTANT'] ?? '';
  static String get getNotes => dotenv.env['GET_NOTES'] ?? '';
  static String get createNote => dotenv.env['CREATE_NOTE'] ?? '';
  static String get updateNote => dotenv.env['UPDATE_NOTE'] ?? '';
  static String get deleteNote => dotenv.env['DELETE_NOTE'] ?? '';
}
