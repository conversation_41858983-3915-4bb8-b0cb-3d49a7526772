class Currency {
  final int id;
  final String country;
  final String currency;
  final String code;
  final String numericCode;
  final int minorUnit;
  final String symbol;

  Currency({
    required this.id,
    required this.country,
    required this.currency,
    required this.code,
    required this.numericCode,
    required this.minorUnit,
    required this.symbol,
  });

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      id: json['id'],
      country: json['country'],
      currency: json['currency'],
      code: json['code'],
      numericCode: json['numeric_code'],
      minorUnit: json['minor_unit'],
      symbol: json['symbol'],
    );
  }
}
