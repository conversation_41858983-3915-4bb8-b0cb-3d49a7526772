import 'package:dio/dio.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/model/note.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotesService {
  static Future<List<Note>> getNotes() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      // Gán token vào header
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      final String url = '${ApiConfig.baseUrl}${ApiEndpoints.getNotes}';
      final response = await dio.get(url);

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Truy cập vào mảng ghi chú trong response: data -> data
        final notesJson = responseData['data']?['data'];

        if (notesJson != null && notesJson is List) {
          // Chuyển từng phần tử JSON thành đối tượng Note
          return notesJson.map<Note>((item) => Note.fromJson(item)).toList();
        } else {
          return [];
        }
      } else {
        throw Exception('Lỗi khi tải ghi chú: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('Kết nối timeout. Vui lòng kiểm tra mạng.');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Timeout khi nhận dữ liệu. Vui lòng thử lại.');
      } else if (e.response?.statusCode == 401) {
        throw Exception('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Không có quyền truy cập.');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Không tìm thấy dữ liệu ghi chú.');
      } else {
        throw Exception('Lỗi kết nối: ${e.message}');
      }
    } catch (e) {
      throw Exception('Có lỗi xảy ra khi tải ghi chú: $e');
    }
  }
}
