import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/model/note.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_picker/file_picker.dart';

class NotesService {
  static Future<List<Note>> getNotes() async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Lấy token từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      // Gán token vào header
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      final String url = '${ApiConfig.baseUrl}${ApiEndpoints.getNotes}';
      final response = await dio.get(url);

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Truy cập vào mảng ghi chú trong response: data -> data
        final notesJson = responseData['data']?['data'];

        if (notesJson != null && notesJson is List) {
          // Chuyển từng phần tử JSON thành đối tượng Note
          return notesJson.map<Note>((item) => Note.fromJson(item)).toList();
        } else {
          return [];
        }
      } else {
        throw Exception('Lỗi khi tải ghi chú: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('Kết nối timeout. Vui lòng kiểm tra mạng.');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Timeout khi nhận dữ liệu. Vui lòng thử lại.');
      } else if (e.response?.statusCode == 401) {
        throw Exception('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Không có quyền truy cập.');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Không tìm thấy dữ liệu ghi chú.');
      } else {
        throw Exception('Lỗi kết nối: ${e.message}');
      }
    } catch (e) {
      throw Exception('Có lỗi xảy ra khi tải ghi chú: $e');
    }
  }

  // Helper method to extract organization ID from user data
  static String? _extractOrganizationId(Map<String, dynamic> userData) {
    try {
      // First priority: Check currentOrganization (based on server response)
      if (userData['currentOrganization'] != null &&
          userData['currentOrganization']['id'] != null) {
        return userData['currentOrganization']['id'].toString();
      }

      // Second priority: Check user_organizations array for organizationId
      if (userData['user_organizations'] is List &&
          (userData['user_organizations'] as List).isNotEmpty) {
        var userOrg = userData['user_organizations'][0];
        if (userOrg is Map && userOrg['organizationId'] != null) {
          return userOrg['organizationId'].toString();
        }

        // Third priority: Check user_organizations[0].organization.id
        if (userOrg is Map && userOrg['organization'] is Map) {
          var orgId = userOrg['organization']['id'];
          if (orgId != null) {
            return orgId.toString();
          }
        }
      }

      // Fourth priority: Check organization field
      if (userData['organization'] != null &&
          userData['organization']['id'] != null) {
        return userData['organization']['id'].toString();
      }

      // Fifth priority: Check direct organizationId field
      if (userData['organizationId'] != null) {
        return userData['organizationId'].toString();
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // Format date for API
  static String _formatDateForAPI(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Update note
  static Future<Note> updateNote({
    required int noteId,
    required String title,
    required String description,
    required DateTime notiDate,
    List<PlatformFile>? newImages,
    bool removeExistingImage = false,
  }) async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Get auth token and user data
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';
      final userDataString = prefs.getString('user_data');

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      if (userDataString == null) {
        throw Exception(
            'Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.');
      }

      // Parse user data to get userId and organizationId
      final userData = jsonDecode(userDataString);
      final userId = userData['id']?.toString();
      final organizationId = _extractOrganizationId(userData);

      if (userId == null || userId.isEmpty) {
        throw Exception(
            'Không tìm thấy ID người dùng. Vui lòng đăng nhập lại.');
      }

      if (organizationId == null || organizationId.isEmpty) {
        throw Exception(
            'Không tìm thấy thông tin tổ chức. Vui lòng đăng nhập lại.');
      }

      // Prepare form data for multipart upload
      final formData = FormData();

      // Add text fields including required userId and organizationId
      formData.fields.addAll([
        MapEntry('title', title.trim()),
        MapEntry('description', description.trim()),
        MapEntry('notiDate', _formatDateForAPI(notiDate)),
        MapEntry('userId', userId),
        MapEntry('organizationId', organizationId),
      ]);

      // Add flag to remove existing image if requested
      if (removeExistingImage) {
        formData.fields.add(const MapEntry('removeImage', 'true'));
      }

      // Add new image files if any
      if (newImages != null && newImages.isNotEmpty) {
        for (var file in newImages) {
          try {
            if (file.bytes != null) {
              formData.files.add(
                MapEntry(
                  'image',
                  MultipartFile.fromBytes(file.bytes!, filename: file.name),
                ),
              );
            }
          } catch (fileError) {
            // Continue with other files instead of failing completely
            continue;
          }
        }
      }

      // Configure Dio headers for multipart
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      // Use the update API endpoint with note ID
      final String url =
          '${ApiConfig.baseUrl}${ApiEndpoints.updateNote}/$noteId';

      final response = await dio.put(
        url,
        data: formData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData['data'] != null) {
          return Note.fromJson(responseData['data']);
        } else {
          throw Exception('Dữ liệu phản hồi không hợp lệ');
        }
      } else {
        throw Exception('Lỗi server: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('Kết nối timeout. Vui lòng kiểm tra mạng.');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Timeout khi nhận dữ liệu. Vui lòng thử lại.');
      } else if (e.response?.statusCode == 401) {
        throw Exception('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Không có quyền truy cập.');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Không tìm thấy ghi chú cần cập nhật.');
      } else {
        throw Exception('Lỗi kết nối: ${e.message}');
      }
    } catch (e) {
      throw Exception('Có lỗi xảy ra khi cập nhật ghi chú: $e');
    }
  }

  // Delete note
  static Future<bool> deleteNote(int noteId) async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không hợp lệ. Vui lòng đăng nhập lại.');
      }

      // Configure Dio headers
      dio.options.headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Use the delete API endpoint with note ID
      final String url =
          '${ApiConfig.baseUrl}${ApiEndpoints.deleteNote}/$noteId';

      final response = await dio.delete(url);

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        throw Exception('Lỗi server: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('Kết nối timeout. Vui lòng kiểm tra mạng.');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Timeout khi nhận dữ liệu. Vui lòng thử lại.');
      } else if (e.response?.statusCode == 401) {
        throw Exception('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Không có quyền truy cập.');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Không tìm thấy ghi chú cần xóa.');
      } else {
        throw Exception('Lỗi kết nối: ${e.message}');
      }
    } catch (e) {
      throw Exception('Có lỗi xảy ra khi xóa ghi chú: $e');
    }
  }
}
