import 'package:flutter/material.dart';
import 'package:du_an_flutter/constants/colors.dart';

class LoadingOverlay extends StatelessWidget {
  const LoadingOverlay({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColors.primary,
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            const Text(
              'Đang tải dữ liệu',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Hàm tiện ích để hiện loading overlay khi chuyển trang
Future<T> showLoadingNavigation<T>({
  required BuildContext context,
  required Future<T> Function() callback,
}) async {
  // Hiển thị loading overlay
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) => const LoadingOverlay(),
  );

  try {
    // Chạy hàm callback (thường là lấy dữ liệu API)
    final result = await callback();
    
    // Đảm bảo loading hiển thị ít nhất 300ms để tránh nhấp nháy
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Đóng loading overlay
    if (context.mounted) {
      Navigator.of(context).pop();
    }
    
    return result;
  } catch (e) {
    // Nếu có lỗi, đóng loading overlay và ném ngoại lệ
    if (context.mounted) {
      Navigator.of(context).pop();
    }
    throw e;
  }
} 