import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:intl/intl.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/services/approval_service.dart';

class PaymentDetail extends StatefulWidget {
  final ActivityItem item;

  const PaymentDetail({Key? key, required this.item}) : super(key: key);

  @override
  State<PaymentDetail> createState() => _PaymentDetailState();
}

class _PaymentDetailState extends State<PaymentDetail> {
  Future<bool> isApprover() async {
    final prefs = await SharedPreferences.getInstance();
    final userRole = prefs.getString('user_role');
    return userRole == 'Approver';
  }

  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        final status = await Permission.storage.request();
        return status.isGranted;
      } else {
        final photos = await Permission.photos.request();
        final videos = await Permission.videos.request();
        final audio = await Permission.audio.request();
        return photos.isGranted && videos.isGranted && audio.isGranted;
      }
    }
    return true;
  }

  Future<void> _openFile(String url, String fileName) async {
    try {
      print('======== FILE DOWNLOAD DEBUG ========');
      print('Attempting to download file:');
      print('File URL: ${ApiConfig.baseUrl + url}');
      print('File name: $fileName');

      final hasPermission = await _requestStoragePermission();
      print('Storage permission granted: $hasPermission');

      if (!hasPermission) {
        throw Exception('Cần cấp quyền truy cập bộ nhớ để tải file');
      }

      final bool? shouldDownload = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('Download file'),
          content: Text('Do you want to download "$fileName"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Download'),
            ),
          ],
        ),
      );

      print('User chose to download: $shouldDownload');

      if (shouldDownload == true) {
        // Get the correct download directory based on Android version
        String downloadPath;
        if (Platform.isAndroid) {
          final androidInfo = await DeviceInfoPlugin().androidInfo;
          final sdkVersion = androidInfo.version.sdkInt;
          print('Android SDK version: $sdkVersion');

          downloadPath = '/storage/emulated/0/Download';
          print('Using download path: $downloadPath');
        } else {
          // Default for iOS or other platforms
          downloadPath = '/storage/emulated/0/Download';
          print('Using default download path: $downloadPath');
        }

        final dir = Directory(downloadPath);
        print('Checking if directory exists: ${dir.path}');

        if (!await dir.exists()) {
          print('Directory does not exist, creating it');
          try {
            await dir.create(recursive: true);
            print('Directory created successfully');
          } catch (e) {
            print('Error creating directory: $e');
            throw Exception('Cannot create download directory: $e');
          }
        } else {
          print('Directory exists');
        }

        // Sanitize the file name to remove invalid characters
        final sanitizedFileName =
            fileName.replaceAll(RegExp(r'[\\/:*?"<>|]'), '_');
        print('Sanitized file name: $sanitizedFileName');

        final filePath = path.join(dir.path, sanitizedFileName);
        print('Full file path for download: $filePath');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Đang tải file...'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Construct the complete URL
        final fullUrl = ApiConfig.baseUrl + url;
        print('Full URL for download: $fullUrl');

        try {
          print('Starting Dio download...');
          await Dio().download(
            fullUrl,
            filePath,
            onReceiveProgress: (received, total) {
              if (total != -1) {
                final percent = (received / total * 100).toStringAsFixed(0);
                print(
                    'Download progress: $received / $total bytes ($percent%)');
              }
            },
          );
          print('Download completed successfully');

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Downloaded: $sanitizedFileName'),
                    const SizedBox(height: 4),
                    Text(
                      'Path: ${dir.path}/$sanitizedFileName',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                duration: const Duration(seconds: 4),
                action: SnackBarAction(
                  label: 'OK',
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                ),
              ),
            );
          }
        } catch (dioError) {
          print('Dio download error: $dioError');
          throw Exception('Download failed: $dioError');
        }
      }
    } catch (e) {
      print('Error in _openFile: $e');
      print('Error stack trace: ${StackTrace.current}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không thể tải file: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('======== END FILE DOWNLOAD DEBUG ========');
    }
  }

  Future<void> _openFileInWeb(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Không thể mở file trên web')),
        );
      }
    }
  }

  Widget _buildFilePreview(String url) {
    // Get file extension from URL
    final extension = url.toLowerCase().split('.').last;

    // Check if it's an image
    if (['jpg', 'jpeg', 'png'].contains(extension)) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.network(
            url,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('Error loading image: $error');
              return const Icon(
                Icons.broken_image,
                color: Colors.red,
                size: 24,
              );
            },
          ),
        ),
      );
    }

    // Return default icon for non-image files
    return Icon(
      extension == 'pdf'
          ? Icons.picture_as_pdf
          : extension == 'doc' || extension == 'docx'
              ? Icons.description
              : Icons.insert_drive_file,
      color: AppColors.primary,
      size: 40,
    );
  }

  String _formatNumber(String number) {
    String cleanNumber = number.replaceAll(RegExp(r'[,.]'), '');
    try {
      final value = int.parse(cleanNumber);
      final format = NumberFormat('#,###', 'en_US');
      return format.format(value).replaceAll(',', '.');
    } catch (e) {
      return number;
    }
  }

  String _getCurrencySymbol(String currencyCode) {
    const symbols = {
      'VND': '₫',
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'KRW': '₩',
      'CHF': 'Fr',
    };
    return symbols[currencyCode] ?? currencyCode;
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'pendingapprover':
        return Colors.orange;
      case 'fail':
        return Colors.red;
      case 'draft':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Widget _buildDetailField(String label, String value, IconData icon,
      {int maxLines = 1}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value.isNotEmpty ? value : 'N/A',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withOpacity(0.1),
            Colors.green.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.attach_money, size: 18, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'Payment Amount',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _getCurrencySymbol(widget.item.currency),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _formatNumber(widget.item.price),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              Text(
                widget.item.currency,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.attach_file, size: 18, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              'Attachments',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${widget.item.attachments!.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...widget.item.attachments!
            .map((attachment) => _buildAttachmentCard(attachment)),
      ],
    );
  }

  Widget _buildAttachmentCard(dynamic attachment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildFilePreview(ApiConfig.baseUrl + attachment.url),
        title: Text(
          attachment.fileName,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        subtitle: Text(
          '${(int.parse(attachment.fileSize) / 1024 / 1024).toStringAsFixed(2)} MB',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        onTap: () => _openFileInWeb(ApiConfig.baseUrl + attachment.url),
        trailing: IconButton(
          icon: const Icon(Icons.download_rounded, color: Colors.blue),
          onPressed: () => _openFile(attachment.url, attachment.fileName),
        ),
      ),
    );
  }

  Widget _buildHistoryLogsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.history, size: 18, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              'History Logs',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 20,
            headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
            columns: const [
              DataColumn(
                label: Text(
                  'Created By',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Description',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Created At',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Updated At',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
            rows: widget.item.historyLogs!.map((log) {
              return DataRow(
                cells: [
                  DataCell(Text('User ${log.createdBy}')),
                  DataCell(Text(log.description)),
                  DataCell(Text(
                      log.createdAt.substring(0, 16).replaceAll('T', ' '))),
                  DataCell(Text(
                      log.updatedAt.substring(0, 16).replaceAll('T', ' '))),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
              label: const Text(
                'Approve',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showApprovalDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Đã duyệt yêu cầu thành công')),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.cancel,
                color: Colors.white,
                size: 24,
              ),
              label: const Text(
                'Reject',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showRejectDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Đã từ chối yêu cầu thành công')),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: isApprover(),
      builder: (context, snapshot) {
        final bool isApproverRole = snapshot.data ?? false;

        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: Text(
                'Payment Details - ${widget.item.title}',
                style: const TextStyle(color: Colors.black),
              ),
              backgroundColor: Colors.white,
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.black),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.white,
              actions: [
                if (!isApproverRole &&
                    ['draft', 'fail']
                        .contains(widget.item.status.toLowerCase()))
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.black),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => PaymentForm(
                            editItem: widget.item,
                            title: 'Chỉnh sửa thanh toán',
                          ),
                        ),
                      );
                    },
                  ),
              ],
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(1),
                child: Container(
                  color: Colors.grey.shade600,
                  height: 1,
                ),
              ),
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Payment Details Section
                        const Text(
                          'Payment Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Title Field
                        _buildDetailField(
                          'Title',
                          widget.item.title,
                          Icons.title,
                        ),
                        const SizedBox(height: 16),

                        // Beneficiary Name Field
                        _buildDetailField(
                          'Beneficiary Name',
                          widget.item.buyerName,
                          Icons.person,
                        ),
                        const SizedBox(height: 16),

                        // Account Number Field
                        _buildDetailField(
                          'Account Number',
                          widget.item.bankNumber,
                          Icons.account_balance,
                        ),
                        const SizedBox(height: 16),

                        // Bank Name Field
                        _buildDetailField(
                          'Bank',
                          widget.item.bankName,
                          Icons.business,
                        ),
                        const SizedBox(height: 16),

                        // Currency Field
                        _buildDetailField(
                          'Currency',
                          widget.item.currency,
                          Icons.monetization_on,
                        ),
                        const SizedBox(height: 16),

                        // Amount Field
                        _buildAmountField(),

                        // Swift Code Field (conditional)
                        if (widget.item.currency != 'VND' &&
                            widget.item.swiftCode != null) ...[
                          const SizedBox(height: 16),
                          _buildDetailField(
                            'Swift Code',
                            widget.item.swiftCode ?? '',
                            Icons.code,
                          ),
                        ],
                        const SizedBox(height: 16),

                        // Description Field
                        _buildDetailField(
                          'Description',
                          widget.item.description,
                          Icons.description,
                          maxLines: 3,
                        ),
                        // Attachments Section
                        if (widget.item.attachments != null &&
                            widget.item.attachments!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildAttachmentsSection(),
                        ],

                        // History Logs Section
                        if (widget.item.historyLogs != null &&
                            widget.item.historyLogs!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildHistoryLogsSection(),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                if (isApproverRole &&
                    widget.item.status.toLowerCase() == 'pendingapprover') ...[
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
