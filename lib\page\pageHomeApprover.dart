import 'package:du_an_flutter/Screen/homeScreenApprover.dart';
import 'package:du_an_flutter/Screen/invoiceForm.dart';
import 'package:du_an_flutter/Screen/notification_screen.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'package:du_an_flutter/Screen/pendingApprover.dart';
import 'package:du_an_flutter/Screen/profileScreen.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/widgets/loading_overlay.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/Screen/homeScreen.dart';
import 'dart:async';

class HomePageApprover extends StatefulWidget {
  const HomePageApprover({super.key});

  @override
  State<HomePageApprover> createState() => _HomePageState();

  // Static method to trigger home refresh from external sources
  static Future<void> triggerHomeRefresh() async {
    return _HomePageState.triggerHomeRefresh();
  }
}

class _HomePageState extends State<HomePageApprover>
    with SingleTickerProviderStateMixin {
  int _bottomNavIndex = 0;
  OverlayEntry? _overlayEntry;
  bool _isKeyboardVisible = false;
  bool _isLoading = false; // Thêm cờ trạng thái đang tải

  // Thêm GlobalKey để truy cập trạng thái của các màn hình
  final homeKey = GlobalKey<State>();
  final listKey = GlobalKey<HomescreenState>();
  final notificationKey = GlobalKey<NotificationScreenState>();
  final profileKey = GlobalKey<ProfilePageState>();
  final pendingApproverKey = GlobalKey<PendingApproverScreenState>();
  int _unreadCount = 0; // Số lượng thông báo chưa đọc
  StreamSubscription? _notificationSubscription; // Đăng ký lắng nghe thông báo

  // Thêm animation controller
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Static instance to access from external sources
  static _HomePageState? _instance;

  @override
  void initState() {
    super.initState();
    // Set static instance for external access
    _instance = this;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // Thời gian hoạt ảnh
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    // Đăng ký lắng nghe sự kiện cập nhật thông báo
    _notificationSubscription =
        NotificationCountListener().stream.listen((count) {
      if (mounted) {
        print('DEBUG - Notification stream update received: count = $count');
        setState(() {
          _unreadCount = count;
          print(
              'Trang chủ người phê duyệt nhận được cập nhật thông báo: $_unreadCount chưa đọc');
        });
      }
    });

    // Lấy số lượng thông báo chưa đọc khi khởi tạo
    Future.delayed(const Duration(milliseconds: 500), () {
      _updateUnreadCount();
    });
  }

  // Phương thức cập nhật số lượng thông báo chưa đọc
  void _updateUnreadCount() {
    if (notificationKey.currentState != null) {
      final unreadCount = notificationKey.currentState!.getUnreadCount();
      print('DEBUG - updateUnreadCount được gọi, unreadCount = $unreadCount');
      setState(() {
        _unreadCount = unreadCount;
      });
      // Đảm bảo cập nhật thông qua NotificationCountListener
      NotificationCountListener().updateUnreadCount(unreadCount);
    }
  }

  // Kiểm tra xem màn hình hiện tại có đang tải không
  bool _isCurrentScreenLoading() {
    try {
      switch (_bottomNavIndex) {
        case 0: // HomeScreenApprover
          return false; // Đơn giản hóa việc thực hiện, không kiểm tra trạng thái cụ thể
        case 1: // HomeScreen (list)
          return false; // Đơn giản hóa việc thực hiện, không kiểm tra trạng thái cụ thể
        case 2: // NotificationScreen
          return notificationKey.currentState?.isLoading ?? false;
        default:
          return false;
      }
    } catch (e) {
      print("Lỗi khi kiểm tra trạng thái tải: $e");
      return false;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Cập nhật số lượng thông báo khi dependencies thay đổi
    _updateUnreadCount();
    print('DEBUG - didChangeDependencies called, updating unread count');
  }

  @override
  void didUpdateWidget(covariant HomePageApprover oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Cập nhật số lượng thông báo khi widget được cập nhật
    _updateUnreadCount();
    print('DEBUG - didUpdateWidget called, updating unread count');
  }

  @override
  void dispose() {
    // Clear static instance
    if (_instance == this) {
      _instance = null;
    }
    _animationController.dispose();
    _notificationSubscription?.cancel(); // Hủy đăng ký lắng nghe
    super.dispose();
  }

  // Static method to trigger home refresh from external sources
  static Future<void> triggerHomeRefresh() async {
    print('HomePageApprover - triggerHomeRefresh called');
    if (_instance != null && _instance!.mounted) {
      // If currently on list tab (index 1), refresh immediately
      if (_instance!._bottomNavIndex == 1) {
        print(
            'HomePageApprover - Currently on list tab, refreshing immediately');
        await _instance!.listKey.currentState?.forceRefresh();
      } else {
        print(
            'HomePageApprover - Not on list tab, will refresh when user switches to list tab');
        // The refresh will happen automatically when user switches to list tab
        // due to the existing refresh mechanism in _switchTabWithLoading
      }
    } else {
      print('HomePageApprover - Instance not available or not mounted');
    }
  }

  void _showOverlay(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;

    _overlayEntry = OverlayEntry(
      builder: (context) => AnimatedBuilder(
        animation: _animation,
        builder: (context, child) => Stack(
          children: [
            // Background với fade animation
            GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.black.withOpacity(0.5 * _animation.value),
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            // Popup với slide và fade animation
            Positioned(
              bottom: isPortrait ? size.height * 0.12 : size.height * 0.2,
              left: size.width * 0.25,
              right: size.width * 0.25,
              child: Transform.translate(
                offset: Offset(0, 50 * (1 - _animation.value)),
                child: Opacity(
                  opacity: _animation.value,
                  child: Material(
                    color: Colors.transparent,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildPopupItem(
                          icon: Icons.description,
                          label: 'Mẫu thanh toán',
                          onTap: () {
                            _removeOverlay();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const PaymentForm()),
                            );
                          },
                        ),
                        const SizedBox(height: 10),
                        _buildPopupItem(
                          icon: Icons.article,
                          label: 'Mẫu hóa đơn',
                          onTap: () {
                            _removeOverlay();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const InvoiceForm()),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _animationController.forward(); // Bắt đầu hiển thị animation
  }

  void _removeOverlay() {
    _animationController.reverse().then((_) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  Widget _buildPopupItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: AppColors.primary),
            const SizedBox(width: 8),
            Text(label, style: const TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }

  // List of icons for the bottom nav bar
  final List<IconData> iconList = [
    Icons.home_rounded,
    Icons.list_alt_rounded,
    Icons.notifications_rounded,
    Icons.person_rounded,
  ];

  // List of labels for the bottom nav bar
  final List<String> iconLabels = [
    'Trang chủ',
    'Danh sách',
    'Danh sách Pending',
    'Thông báo',
    'Cá nhân',
  ];

  // Get activity items from Homescreen widget
  List<ActivityItem> get _activityItems {
    final homescreenState = listKey.currentState;
    if (homescreenState != null) {
      return homescreenState.items;
    }
    return [];
  }

  // List of pages
  List<Widget> get _pages => [
        HomeScreenApprover(
          key: homeKey,
          onNavigateToHome: () {
            // Navigate to Homescreen tab (index 1)
            _switchTabWithLoading(1);
          },
          activityItems: _activityItems,
        ),
        Homescreen(key: listKey),
        PendingApproverScreen(key: pendingApproverKey),
        NotificationScreen(key: notificationKey),
        ProfilePage(key: profileKey),
      ];

  // Phương thức chuyển tab với trạng thái đang tải
  Future<void> _switchTabWithLoading(int newIndex) async {
    if (_isLoading) return;

    // Đảm bảo hủy focus trước khi chuyển tab
    FocusManager.instance.primaryFocus?.unfocus();

    // Lưu index cũ trước khi thay đổi
    final oldIndex = _bottomNavIndex;

    // Chuyển tab ngay lập tức để cung cấp phản hồi tức thì
    setState(() {
      _bottomNavIndex = newIndex;
      _isLoading = true;
    });

    try {
      // Hiển thị hiệu ứng tải trong thời gian ngắn
      await Future.delayed(const Duration(milliseconds: 300));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // Nếu chuyển sang tab thông báo, cập nhật số lượng thông báo chưa đọc
      if (newIndex == 2) {
        _updateUnreadCount();
      }

      // Nếu chuyển từ tab thông báo sang tab khác, cũng cập nhật số lượng
      if (oldIndex == 2 && newIndex != 2) {
        _updateUnreadCount();
      }
    } catch (e) {
      print("Lỗi khi chuyển tab: $e");
      if (mounted) {
        setState(() => _isLoading = false);
      }
    } finally {
      // Đảm bảo luôn đặt _isLoading về false ngay cả khi có lỗi
      Future.delayed(const Duration(milliseconds: 350), () {
        if (mounted && _isLoading) {
          setState(() => _isLoading = false);
          // Đảm bảo không có focus sau khi tải xong
          FocusManager.instance.primaryFocus?.unfocus();
        }
      });
    }

    // Force refresh of list screen (Homescreen) when switching to that tab (index 1)
    // BUT only if not currently in search mode to preserve search results
    if (newIndex == 1 && listKey.currentState != null) {
      // Use a small delay to ensure the tab switch animation completes
      Future.delayed(const Duration(milliseconds: 100), () {
        // Check if user is currently searching before forcing refresh
        final currentState = listKey.currentState!;
        // Only refresh if not in search mode (search query is empty)
        if (!currentState.isInSearchMode) {
          currentState.forceRefresh();
        } else {
          print(
              'HomePageApprover - Skipping refresh because user is in search mode');
        }
      });
    }

    // Force refresh of PendingApproverScreen when switching to that tab (index 2)
    if (newIndex == 2 && pendingApproverKey.currentState != null) {
      // Use a small delay to ensure the tab switch animation completes
      Future.delayed(const Duration(milliseconds: 100), () {
        pendingApproverKey.currentState!.forceReload();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    _isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;

    print('DEBUG - pageHomeApprover build: _unreadCount = $_unreadCount');

    return Scaffold(
      body: Stack(
        children: [
          // Sử dụng IndexedStack để giữ trạng thái của các trang
          IndexedStack(
            index: _bottomNavIndex,
            children: _pages,
          ),
          // Hiển thị lớp phủ đang tải
          if (_isLoading) const LoadingOverlay()
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: 'Trang chủ',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_alt_outlined),
                activeIcon: Icon(Icons.list_alt),
                label: 'Danh sách',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_alt_outlined),
                activeIcon: Icon(Icons.list_alt),
                label: 'Danh sách Pending',
              ),
              BottomNavigationBarItem(
                icon: Badge(
                  label: Text(
                    _unreadCount.toString(),
                    style: TextStyle(color: Colors.white, fontSize: 10),
                  ),
                  child: Icon(Icons.notifications_outlined),
                ),
                activeIcon: Badge(
                  label: Text(
                    _unreadCount.toString(),
                    style: TextStyle(color: Colors.white, fontSize: 10),
                  ),
                  child: Icon(Icons.notifications),
                ),
                label: 'Thông báo',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_outline),
                activeIcon: Icon(Icons.person),
                label: 'Cá nhân',
              ),
            ],
            currentIndex: _bottomNavIndex,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: Colors.grey,
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            selectedLabelStyle:
                TextStyle(color: Colors.grey[800], fontSize: 12),
            unselectedLabelStyle:
                TextStyle(color: Colors.grey[400], fontSize: 12),
            elevation: 0,
            onTap: (index) async {
              // Nếu đang tải, hiển thị thông báo và ngăn chuyển tab
              if (_isLoading) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Đang chuyển trang, vui lòng đợi...'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 1),
                  ),
                );
                return;
              }

              // Nếu trang hiện tại đang tải dữ liệu, hiển thị thông báo và ngăn chuyển tab
              if (_isCurrentScreenLoading()) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Vui lòng đợi dữ liệu đang tải xong...'),
                    backgroundColor: Colors.orange,
                    duration: Duration(seconds: 2),
                  ),
                );
                return;
              }

              // Nếu chuyển từ trang cá nhân sang trang khác, cần xác nhận thay đổi chưa lưu
              if (_bottomNavIndex == 3 && index != 3) {
                final profileState = profileKey.currentState;
                if (profileState != null && profileState.isEdited) {
                  final canLeave = await profileState.handleTabNavigation();
                  if (!canLeave) {
                    return;
                  }
                }
              }

              // Sử dụng phương thức chuyển tab với trạng thái đang tải
              _switchTabWithLoading(index);
            },
          ),
        ),
      ),
    );
  }
}
