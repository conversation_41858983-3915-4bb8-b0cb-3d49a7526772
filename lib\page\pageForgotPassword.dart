import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/page/pageSendOTP.dart';
import 'package:flutter/material.dart';
import 'dart:math';

class pageForgotPassword extends StatefulWidget {
  const pageForgotPassword({super.key});

  @override
  State<pageForgotPassword> createState() => _pageForgotPasswordState();
}

class _pageForgotPasswordState extends State<pageForgotPassword> {
  bool isValidEmail = false;
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateEmail);
  }

  void _validateEmail() {
    setState(() {
      // Check if email matches Gmail format
      isValidEmail = _emailController.text.endsWith('@gmail.com') &&
          _emailController.text.length > 10; // "@gmail.com" is 10 chars
    });
  }

  @override
  void dispose() {
    _emailController.removeListener(_validateEmail);
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    // Sử dụng giá trị nhỏ hơn giữa width và height để tính toán
    final minSize = min(screenWidth, screenHeight);

    return Scaffold(
      appBar: AppBar(
        title:
            const Text('Quên mật khẩu', style: TextStyle(color: Colors.white)),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white), // Thêm dòng này
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: minSize * 0.05),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: minSize * 0.85, // Cùng chiều rộng với TextField
                    alignment: Alignment.centerLeft, // Căn trái
                    child: Text(
                      'Nhập Email của bạn',
                      style: TextStyle(
                        fontSize: minSize * 0.04,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.02),
                  SizedBox(
                    width: minSize * 0.85,
                    child: TextField(
                      controller: _emailController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: '<EMAIL>',
                        errorText: _emailController.text.isEmpty
                            ? 'Email không được để trống'
                            : (!_emailController.text.endsWith('@gmail.com') ||
                                    _emailController.text.length <= 10)
                                ? 'Email phải có định dạng @gmail.com'
                                : null,
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                  ),
                  SizedBox(height: minSize * 0.04),
                  SizedBox(
                    width: minSize * 0.6,
                    height: minSize * 0.1,
                    child: ElevatedButton(
                      onPressed: () {
                        if (_emailController.text.isNotEmpty &&
                            _emailController.text.endsWith('@gmail.com') &&
                            _emailController.text.length > 10) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const pageSendOTP(),
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(minSize * 0.03),
                        ),
                      ),
                      child: Text(
                        'Tiếp theo',
                        style: TextStyle(
                            fontSize: minSize * 0.04, color: Colors.white),
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
