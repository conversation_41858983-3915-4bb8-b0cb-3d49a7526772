class User {
  final int id;
  final String fullname;
  final String email;
  final String phone;
  final String? image;
  final int status;
  final int organizationId;
  final int roleId;
  final String language;
  final String cccd;
  final String birthday;
  final String grantedDate;
  final List<Bank> banks;
  final Role role;
  final Organization organization;

  User({
    required this.id,
    required this.fullname,
    required this.email,
    required this.phone,
    this.image,
    required this.status,
    required this.organizationId,
    required this.roleId,
    required this.language,
    required this.cccd,
    required this.birthday,
    required this.grantedDate,
    required this.banks,
    required this.role,
    required this.organization,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      fullname: json['fullname'],
      email: json['email'],
      phone: json['phone'],
      image: json['image'],
      status: json['status'],
      organizationId: json['organizationId'],
      roleId: json['roleId'],
      language: json['language'],
      cccd: json['cccd'],
      birthday: json['birthday'],
      grantedDate: json['grantedDate'],
      banks: (json['banks'] as List<dynamic>)
          .map((bank) => Bank.fromJson(bank))
          .toList(),
      role: Role.fromJson(json['role']),
      organization: Organization.fromJson(json['organization']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'email': email,
      'phone': phone,
      'image': image,
      'status': status,
      'organizationId': organizationId,
      'roleId': roleId,
      'language': language,
      'cccd': cccd,
      'birthday': birthday,
      'grantedDate': grantedDate,
      'banks': banks.map((bank) => bank.toJson()).toList(),
      'role': role.toJson(),
      'organization': organization.toJson(),
    };
  }
}

// Add new Bank class
class Bank {
  final String id;
  final String bankName;
  final String bankNumber;
  final String accountHolder;

  Bank({
    required this.id,
    required this.bankName,
    required this.bankNumber,
    required this.accountHolder,
  });

  factory Bank.fromJson(Map<String, dynamic> json) {
    return Bank(
      id: json['id'] ?? '',
      bankName: json['bank_name'] ?? '', // Changed from bankName
      bankNumber: json['card_number'] ?? '', // Changed from bankNumber
      accountHolder: json['account_name'] ?? '', // Changed from accountHolder
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bank_name': bankName, // Changed to match API format
      'card_number': bankNumber, // Changed to match API format
      'account_name': accountHolder, // Changed to match API format
    };
  }
}

class Role {
  final int id;
  final String name;

  Role({required this.id, required this.name});

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class Organization {
  final int id;
  final String name;

  Organization({required this.id, required this.name});

  factory Organization.fromJson(Map<String, dynamic> json) {
    return Organization(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}
