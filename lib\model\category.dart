class Category {
  final int id;
  final String name;
  final String language;
  final String parentLanguage;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  Category({
    required this.id,
    required this.name,
    required this.language,
    required this.parentLanguage,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      language: json['language'],
      parentLanguage: json['parentLanguage'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'language': language,
      'parentLanguage': parentLanguage,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }
} 