import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/page/pageForgotPassword.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:du_an_flutter/page/pageHomeApprover.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:shared_preferences/shared_preferences.dart';

class pageLogin extends StatefulWidget {
  const pageLogin({super.key});

  @override
  State<pageLogin> createState() => _pageLoginState();
}

class _pageLoginState extends State<pageLogin> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _orgSearchController = TextEditingController();
  bool isLoading = false;
  bool _obscurePassword = true;
  bool isEmailValid = false;

  // Thêm biến cho Organizations
  List<Map<String, dynamic>> organizations = [];
  Map<String, dynamic>? selectedOrganization;
  bool isLoadingOrganizations = true;
  List<Map<String, dynamic>> filteredOrganizations = [];
  bool isShowingSuggestions = false;

  // Email auto-suggestion variables
  List<String> emailDomains = [
    '@gmail.com', // Most popular first
    '@yahoo.com',
    '@outlook.com',
    '@hotmail.com',
    '@icloud.com',
  ];
  List<String> emailSuggestions = [];
  bool isShowingEmailSuggestions = false;
  String currentEmailInput = '';
  FocusNode _emailFocusNode = FocusNode();

  void _validateEmail() {
    setState(() {
      // Cập nhật regex để chấp nhận nhiều domain phổ biến
      final emailRegex =
          RegExp(r'^[\w-\.]+@(gmail|yahoo|outlook|hotmail|icloud)\.com$');
      isEmailValid = emailRegex.hasMatch(_emailController.text);
    });
  }

  // Hàm tạo email suggestions
  void _generateEmailSuggestions(String input) {
    currentEmailInput = input;

    // Clear suggestions if input is empty
    if (input.isEmpty) {
      setState(() {
        emailSuggestions = [];
        isShowingEmailSuggestions = false;
      });
      return;
    }

    // If input contains @, don't show suggestions
    if (input.contains('@')) {
      setState(() {
        emailSuggestions = [];
        isShowingEmailSuggestions = false;
      });
      return;
    }

    // Generate suggestions by appending domains to the username
    List<String> suggestions =
        emailDomains.map((domain) => input + domain).toList();

    setState(() {
      emailSuggestions = suggestions;
      isShowingEmailSuggestions = suggestions.isNotEmpty;
    });
  }

  // Hàm chọn email suggestion
  void _selectEmailSuggestion(String suggestion) {
    setState(() {
      _emailController.text = suggestion;
      emailSuggestions = [];
      isShowingEmailSuggestions = false;
    });
    _validateEmail();
    _validateFields();
    // Move focus away from email field
    FocusScope.of(context).unfocus();
  }

  // Hàm ẩn email suggestions
  void _hideEmailSuggestions() {
    setState(() {
      emailSuggestions = [];
      isShowingEmailSuggestions = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _emailController.addListener(_validateEmail);
    _emailController.addListener(_validateFields);
    _emailController.addListener(() {
      _generateEmailSuggestions(_emailController.text);
    });
    _passwordController.addListener(_validateFields);
    _loadOrganizations(); // Gọi API lấy danh sách tổ chức khi khởi tạo
  }

  // Thêm hàm tải danh sách tổ chức
  Future<void> _loadOrganizations() async {
    try {
      final url = '${ApiConfig.baseUrl}${ApiEndpoints.getOrganizations}';
      print('Loading organizations from: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.headers,
      );

      print('Organizations Response Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Add null checks for API response
        if (data == null || data['data'] == null) {
          print('API returned null data or missing data field');
          setState(() {
            organizations = [];
            isLoadingOrganizations = false;
          });
          return;
        }

        final orgList = data['data'] as List;

        setState(() {
          organizations = List<Map<String, dynamic>>.from(
              orgList.where((item) => item != null));
          isLoadingOrganizations = false;
          selectedOrganization = null; // Để người dùng chọn
        });

        print('Loaded ${organizations.length} organizations');
      } else {
        print('Failed to load organizations: ${response.body}');
        setState(() => isLoadingOrganizations = false);
      }
    } catch (e) {
      print('Error loading organizations: $e');
      setState(() => isLoadingOrganizations = false);
    }
  }

  Future<void> _login() async {
    // Kiểm tra các trường trước khi gọi API
    if (selectedOrganization == null) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thông báo'),
          content: const Text('Vui lòng chọn tổ chức để đăng nhập'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    if (_emailController.text.isEmpty) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thông báo'),
          content: const Text('Vui lòng nhập email'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    if (!isEmailValid) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thông báo'),
          content: const Text('Email phải có định dạng @gmail.com'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    if (_passwordController.text.isEmpty) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thông báo'),
          content: const Text('Vui lòng nhập mật khẩu'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final url = '${ApiConfig.baseUrl}${ApiEndpoints.login}';
      print('API URL: $url'); // In đường dẫn API

      final requestBody = {
        'email': _emailController.text,
        'password': _passwordController.text,
        // Thêm organizationId vào request body nếu đã chọn tổ chức
        if (selectedOrganization != null)
          'organizationId': selectedOrganization!['id'],
      };
      print('Request Body: ${jsonEncode(requestBody)}'); // In request body

      final response = await http.post(
        Uri.parse(url),
        headers: ApiConfig.headers,
        body: jsonEncode(requestBody),
      );

      print('Response Status Code: ${response.statusCode}'); // In status code
      print('Response Body: ${response.body}'); // In response body

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final userData = data['data'];

        // Lưu token
        await ApiConfig.setToken(userData['access_token']);

        // Lưu thông tin user và role
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_data', jsonEncode(userData));

        // Xác định role theo cấu trúc API mới
        String roleName = "Submitter"; // Mặc định là Submitter

        try {
          if (userData['currentRole'] != null) {
            // Cấu trúc cũ
            roleName = userData['currentRole']['name'];
          } else if (userData['user_organizations'] != null &&
              userData['user_organizations'].isNotEmpty &&
              userData['user_organizations'][0]['role'] != null) {
            // Cấu trúc mới
            roleName = userData['user_organizations'][0]['role']['name'];
          }

          // Lưu role name
          await prefs.setString('user_role', roleName);

          print('Đã xác định role: $roleName');
        } catch (e) {
          print('Lỗi khi xác định role: $e');
          // Trong trường hợp lỗi, giữ mặc định là Submitter
        }

        // Kiểm tra role và điều hướng
        if (roleName == 'Submitter') {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const HomePage()),
            (route) => false,
          );
        } else if (roleName == 'Approver') {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const HomePageApprover()),
            (route) => false,
          );
        }
      } else {
        final errorData = jsonDecode(response.body);
        final errorMessage = errorData['message'] ?? 'Đăng nhập thất bại';

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Lỗi đăng nhập'),
            content: Text(errorMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      print('Error: $e'); // In lỗi nếu có
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Lỗi kết nối'),
          content: Text('Không thể kết nối tới server: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // Thêm hàm kiểm tra tất cả các trường đã được điền
  bool _isFormValid() {
    return selectedOrganization != null &&
        _emailController.text.isNotEmpty &&
        isEmailValid &&
        _passwordController.text.isNotEmpty;
  }

  // Hàm cập nhật trạng thái khi người dùng nhập liệu
  void _validateFields() {
    setState(() {
      // Cập nhật UI
    });
  }

  // Hàm lọc tổ chức dựa trên văn bản người dùng nhập
  void _filterOrganizations(String query) {
    print('Filtering organizations with query: "$query"');
    print('Total organizations: ${organizations.length}');

    // Luôn hiển thị tất cả các tổ chức nếu query trống
    if (query.isEmpty) {
      setState(() {
        filteredOrganizations = List.from(organizations);
        isShowingSuggestions = filteredOrganizations.isNotEmpty;
      });
      print('Showing all ${filteredOrganizations.length} organizations');
      return;
    }

    final filtered = organizations.where((org) {
      // Add null check
      if (org == null || org['name'] == null) return false;
      final name = org['name'].toString().toLowerCase();
      return name.contains(query.toLowerCase());
    }).toList();

    setState(() {
      filteredOrganizations = filtered;
      isShowingSuggestions = true;
    });

    print('Found ${filteredOrganizations.length} matching organizations');
  }

  // Hàm chọn tổ chức từ gợi ý
  void _selectOrganization(Map<String, dynamic> org) {
    // Add null check
    if (org == null) return;

    setState(() {
      selectedOrganization = org;
      _orgSearchController.text = org['name'] ?? '';
      isShowingSuggestions = false;
      _validateFields();
    });
  }

  @override
  void dispose() {
    _emailController.removeListener(_validateEmail);
    _emailController.dispose();
    _passwordController.dispose();
    _orgSearchController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final minSize = min(screenWidth, screenHeight);

    return GestureDetector(
        onTap: () {
          // Hide email suggestions when tapping outside
          _hideEmailSuggestions();
          // Also hide organization suggestions
          setState(() {
            isShowingSuggestions = false;
          });
          // Unfocus any focused text field
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: minSize * 0.05),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      SizedBox(height: minSize * 0.02),
                      Image.asset(
                        'assets/images/logo.jpg',
                        width: minSize * 0.4,
                        height: minSize * 0.4,
                      ),
                      SizedBox(height: minSize * 0.02),
                      // Di chuyển dropdown Organizations lên trên
                      SizedBox(
                        width: minSize * 0.85,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: isLoadingOrganizations
                                  ? const Center(
                                      child: Padding(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 15),
                                        child: SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                              strokeWidth: 2),
                                        ),
                                      ),
                                    )
                                  : Column(
                                      children: [
                                        TextField(
                                          controller: _orgSearchController,
                                          decoration: InputDecoration(
                                            hintText: "Chọn tổ chức của bạn",
                                            border: InputBorder.none,
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    vertical: 15,
                                                    horizontal: 12),
                                            prefixIcon: const Icon(
                                                Icons.business_outlined,
                                                color: Colors.grey),
                                            prefixIconConstraints:
                                                const BoxConstraints(
                                                    minWidth: 48,
                                                    minHeight: 48),
                                            suffixIcon: IconButton(
                                              icon: Icon(isShowingSuggestions
                                                  ? Icons.arrow_drop_up
                                                  : Icons.arrow_drop_down),
                                              onPressed: () {
                                                setState(() {
                                                  // Toggle suggestions visibility
                                                  if (isShowingSuggestions) {
                                                    isShowingSuggestions =
                                                        false;
                                                  } else {
                                                    filteredOrganizations =
                                                        List.from(
                                                            organizations);
                                                    isShowingSuggestions = true;
                                                  }
                                                });
                                              },
                                            ),
                                          ),
                                          onChanged: (value) {
                                            _filterOrganizations(value);
                                          },
                                          onTap: () {
                                            // Show all organizations when tapping the field if no text
                                            if (_orgSearchController
                                                .text.isEmpty) {
                                              setState(() {
                                                filteredOrganizations =
                                                    List.from(organizations);
                                                isShowingSuggestions = true;
                                              });
                                            }
                                          },
                                        ),
                                        if (isShowingSuggestions)
                                          Container(
                                            constraints:
                                                BoxConstraints(maxHeight: 200),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.grey
                                                      .withOpacity(0.3),
                                                  spreadRadius: 1,
                                                  blurRadius: 2,
                                                  offset: const Offset(0, 1),
                                                ),
                                              ],
                                            ),
                                            child: filteredOrganizations.isEmpty
                                                ? ListTile(
                                                    dense: true,
                                                    title: const Text(
                                                        'Không tìm thấy tổ chức'),
                                                    enabled: false,
                                                  )
                                                : ListView.builder(
                                                    shrinkWrap: true,
                                                    padding: EdgeInsets.zero,
                                                    itemCount:
                                                        filteredOrganizations
                                                            .length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final org =
                                                          filteredOrganizations[
                                                              index];
                                                      return ListTile(
                                                        dense: true,
                                                        title:
                                                            Text(org['name']),
                                                        onTap: () =>
                                                            _selectOrganization(
                                                                org),
                                                      );
                                                    },
                                                  ),
                                          ),
                                      ],
                                    ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: minSize * 0.04),
                      SizedBox(
                        width: minSize * 0.85,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextField(
                              controller: _emailController,
                              focusNode: _emailFocusNode,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: 'Email',
                                hintText: 'Nhập email',
                                contentPadding: const EdgeInsets.symmetric(
                                    vertical: 15, horizontal: 12),
                                prefixIconConstraints: const BoxConstraints(
                                    minWidth: 48, minHeight: 48),
                                errorText: _emailController.text.isNotEmpty &&
                                        !isEmailValid
                                    ? 'Email phải có định dạng hợp lệ (@gmail.com, @yahoo.com, etc.)'
                                    : null,
                                prefixIcon: const Icon(
                                  Icons.email_outlined,
                                  color: Colors.grey,
                                ),
                                suffixIcon: isShowingEmailSuggestions
                                    ? IconButton(
                                        icon: const Icon(Icons.clear, size: 20),
                                        onPressed: () {
                                          setState(() {
                                            isShowingEmailSuggestions = false;
                                            emailSuggestions = [];
                                          });
                                        },
                                      )
                                    : null,
                              ),
                              keyboardType: TextInputType.emailAddress,
                              onTap: () {
                                // Show suggestions when tapping the field if there's text without @
                                if (_emailController.text.isNotEmpty &&
                                    !_emailController.text.contains('@')) {
                                  _generateEmailSuggestions(
                                      _emailController.text);
                                }
                              },
                            ),
                            // Email suggestions dropdown
                            if (isShowingEmailSuggestions &&
                                emailSuggestions.isNotEmpty)
                              Container(
                                constraints:
                                    const BoxConstraints(maxHeight: 200),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border:
                                      Border.all(color: Colors.grey.shade300),
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(4),
                                    bottomRight: Radius.circular(4),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.3),
                                      spreadRadius: 1,
                                      blurRadius: 3,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  padding: EdgeInsets.zero,
                                  itemCount: emailSuggestions.length,
                                  itemBuilder: (context, index) {
                                    final suggestion = emailSuggestions[index];
                                    return ListTile(
                                      dense: true,
                                      leading: const Icon(
                                        Icons.email_outlined,
                                        size: 20,
                                        color: Colors.blue,
                                      ),
                                      title: Text(
                                        suggestion,
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                      onTap: () =>
                                          _selectEmailSuggestion(suggestion),
                                      hoverColor: Colors.blue.shade50,
                                    );
                                  },
                                ),
                              ),
                          ],
                        ),
                      ),
                      SizedBox(height: minSize * 0.04),
                      SizedBox(
                        width: minSize * 0.85,
                        child: TextField(
                          controller: _passwordController,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            labelText: 'Password',
                            hintText: 'Nhập mật khẩu',
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 15, horizontal: 12),
                            prefixIconConstraints: const BoxConstraints(
                                minWidth: 48, minHeight: 48),
                            prefixIcon: const Icon(
                              Icons.lock_outline,
                              color: Colors.grey,
                            ),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                          ),
                          obscureText: _obscurePassword,
                        ),
                      ),
                      SizedBox(height: minSize * 0.02),
                      Container(
                        width: minSize *
                            0.85, // Giữ cùng độ rộng với các TextField
                        alignment: Alignment.centerRight, // Căn phải
                        child: TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        const pageForgotPassword()),
                              );
                            },
                            child: Text(
                              "Quên mật khẩu ?",
                              style: TextStyle(
                                  fontSize: minSize * 0.04,
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold),
                            )),
                      ),
                      SizedBox(height: minSize * 0.02),
                      SizedBox(
                        width: minSize * 0.6,
                        height: minSize * 0.1,
                        child: ElevatedButton(
                          // Cập nhật điều kiện để bật nút đăng nhập
                          onPressed: !isLoading
                              ? _login
                              : null, // Chỉ disable nếu đang loading
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            disabledBackgroundColor: Colors.grey,
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(minSize * 0.03),
                            ),
                          ),
                          child: isLoading
                              ? const CircularProgressIndicator(
                                  color: Colors.white)
                              : Text(
                                  'Đăng nhập',
                                  style: TextStyle(
                                      fontSize: minSize * 0.04,
                                      color: Colors.white),
                                ),
                        ),
                      ),
                      // Cập nhật thông báo lỗi hiển thị

                      SizedBox(height: minSize * 0.02),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ));
  }
}
