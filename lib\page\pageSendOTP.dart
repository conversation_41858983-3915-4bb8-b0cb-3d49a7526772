import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/page/pageChangePassword.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';
import 'dart:async'; // Thêm import này

class pageSendOTP extends StatefulWidget {
  final String? serverOTP; // Thêm tham số để nhận OTP từ bên ngoài

  const pageSendOTP(
      {super.key, this.serverOTP = "123456" // Giá trị mặc định để test
      });

  @override
  State<pageSendOTP> createState() => _pageSendOTP();
}

class _pageSendOTP extends State<pageSendOTP> {
  String userOTP = "";
  bool isValidOTP = false;
  bool canResendOTP = false;
  int remainingSeconds = 30; // Thời gian đếm ngược
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    setState(() {
      canResendOTP = false;
      remainingSeconds = 30;
    });

    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (remainingSeconds > 0) {
          remainingSeconds--;
        } else {
          canResendOTP = true;
          timer.cancel();
        }
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel(); // Hủy timer khi widget bị dispose
    super.dispose();
  }

  // Function to check if passwords match

  void validateOTP(String value) {
    if (value.length == 6) {
      // Chỉ kiểm tra khi đã nhập đủ 6 số
      if (value != widget.serverOTP) {
        // Hiện popup thông báo lỗi
        showDialog(
          context: context,
          barrierDismissible: false, // Người dùng phải nhấn OK
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Thông báo'),
              content: const Text('Mã OTP không chính xác!'),
              actions: <Widget>[
                TextButton(
                  child: const Text('OK'),
                  onPressed: () {
                    Navigator.of(context).pop(); // Đóng dialog
                    Navigator.of(context).pop(); // Quay về trang trước
                  },
                ),
              ],
            );
          },
        );
      } else {
        setState(() {
          userOTP = value;
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const pageChangePassword()),
            (route) => false, // This removes all previous routes
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final minSize = min(screenWidth, screenHeight);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Nhập OTP', style: TextStyle(color: Colors.white)),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: minSize * 0.05),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    width: minSize * 0.85, // Cùng chiều rộng với TextField
                    alignment: Alignment.centerLeft, // Căn trái
                    child: Text(
                      'Nhập mã OTP',
                      style: TextStyle(
                        fontSize: minSize * 0.04,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.02),
                  SizedBox(
                    width: minSize * 0.85,
                    child: TextField(
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Nhập 6 số OTP',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      onChanged: validateOTP,
                      readOnly: isValidOTP, // Khóa TextField khi OTP đã đúng
                    ),
                  ),
                  SizedBox(height: minSize * 0.04),
                  SizedBox(
                    width: minSize * 0.41,
                    height: minSize * 0.12,
                    child: ElevatedButton(
                      onPressed: canResendOTP && !isValidOTP
                          ? () {
                              // Xử lý gửi lại OTP
                              startTimer(); // Bắt đầu đếm ngược lại
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        disabledBackgroundColor: Colors.grey,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(minSize * 0.03),
                        ),
                      ),
                      child: Text(
                        textAlign: TextAlign.center,
                        canResendOTP
                            ? 'Gửi lại OTP'
                            : 'Gửi lại OTP (${remainingSeconds}s)',
                        style: TextStyle(
                          fontSize: minSize * 0.04,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
