import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/Screen/paymentForm.dart';
import 'package:du_an_flutter/Screen/invoiceForm.dart';
import 'package:flutter/material.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:dio/dio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/services/approval_service.dart'; // Thêm import
import 'package:intl/intl.dart';

class InvoiceDetail extends StatefulWidget {
  final ActivityItem item;

  const InvoiceDetail({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  State<InvoiceDetail> createState() => _InvoiceDetailState();
}

class _InvoiceDetailState extends State<InvoiceDetail> {
  // Thêm hàm check role
  Future<bool> isApprover() async {
    final prefs = await SharedPreferences.getInstance();
    final userRole = prefs.getString('user_role');
    return userRole == 'Approver';
  }

  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        // Android 12 hoặc thấp hơn
        final status = await Permission.storage.request();
        return status.isGranted;
      } else {
        // Android 13 trở lên
        final photos = await Permission.photos.request();
        final videos = await Permission.videos.request();
        final audio = await Permission.audio.request();
        return photos.isGranted && videos.isGranted && audio.isGranted;
      }
    }
    return true;
  }

  Future<void> _openFile(String url, String fileName) async {
    try {
      // Kiểm tra quyền truy cập
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        throw Exception('Cần cấp quyền truy cập bộ nhớ để tải file');
      }

      // Hiển thị dialog xác nhận
      final bool? shouldDownload = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('Download file'),
          content: Text('Do you want to download "$fileName"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Download'),
            ),
          ],
        ),
      );

      if (shouldDownload == true) {
        // Lấy đường dẫn thư mục Downloads
        final dir = Directory('/storage/emulated/0/Download');
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        final filePath = path.join(dir.path, fileName);

        // Hiển thị thông báo đang tải
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Downloading file...')),
          );
        }

        // Tải file
        await Dio().download(
          url,
          filePath,
          onReceiveProgress: (received, total) {
            if (total != -1) {
              print((received / total * 100).toStringAsFixed(0) + "%");
            }
          },
        );

        // Hiển thị thông báo thành công
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Downloaded: $fileName'),
                  const SizedBox(height: 4),
                  Text(
                    'Path: ${dir.path}/$fileName',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
              duration: const Duration(seconds: 4),
              action: SnackBarAction(
                label: 'OK',
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error downloading file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không thể tải file: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _openFileInWeb(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Không thể mở file trên web')),
        );
      }
    }
  }

  Widget _buildFilePreview(String fileType, String url) {
    // Get file extension from URL
    final extension = url.split('.').last.toLowerCase();

    // Check if it's an image
    if (['jpg', 'jpeg', 'png'].contains(extension)) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.network(
            url,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('Error loading image: $error');
              return const Icon(
                Icons.broken_image,
                color: Colors.red,
                size: 24,
              );
            },
          ),
        ),
      );
    }

    // Return default icon for non-image files
    return Icon(
      extension == 'pdf'
          ? Icons.picture_as_pdf
          : extension == 'doc' || extension == 'docx'
              ? Icons.description
              : Icons.insert_drive_file,
      color: AppColors.primary,
      size: 40,
    );
  }

  Widget _buildAttachmentCard(dynamic attachment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildFilePreview(
          attachment.mimeType,
          ApiConfig.baseUrl + attachment.url,
        ),
        title: Text(
          attachment.fileName,
          style: const TextStyle(fontSize: 14),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        subtitle: Text(
          '${(int.parse(attachment.fileSize) / 1024 / 1024).toStringAsFixed(2)} MB',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        onTap: () => _openFileInWeb(ApiConfig.baseUrl + attachment.url),
        trailing: IconButton(
          icon: const Icon(Icons.download_rounded),
          onPressed: () => _openFile(
            ApiConfig.baseUrl + attachment.url,
            attachment.fileName,
          ),
        ),
      ),
    );
  }

  String _formatNumber(String number) {
    // Xóa bỏ các dấu phân cách hiện có (nếu có)
    String cleanNumber = number.replaceAll(RegExp(r'[,.]'), '');

    try {
      final value = int.parse(cleanNumber);
      final format = NumberFormat('#,###', 'en_US');
      return format.format(value).replaceAll(',', '.');
    } catch (e) {
      return number;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'pendingapprover':
        return Colors.orange;
      case 'fail':
        return Colors.red;
      case 'draft':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  Widget _buildDetailField(String label, String value, IconData icon,
      {int maxLines = 1}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value.isNotEmpty ? value : 'N/A',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountField() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.blue.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.price_change, size: 18, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                'Unit Price',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _getCurrencySymbol(widget.item.currency),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _formatNumber(widget.item.price),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
              Text(
                widget.item.currency,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTotalPriceField() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.withOpacity(0.1),
            Colors.green.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.calculate, size: 18, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'Total Amount',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                _getCurrencySymbol(widget.item.currency),
                style: const TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _formatNumber(widget.item.totalPrice ?? '0'),
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              Text(
                widget.item.currency,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getCurrencySymbol(String currencyCode) {
    const symbols = {
      'VND': '₫',
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'KRW': '₩',
      'CHF': 'Fr',
    };
    return symbols[currencyCode] ?? currencyCode;
  }

  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.attach_file, size: 18, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              'Attachments',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${widget.item.attachments!.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...widget.item.attachments!
            .map((attachment) => _buildAttachmentCard(attachment)),
      ],
    );
  }

  Widget _buildHistoryLogsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.history, size: 18, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              'History Logs',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            columnSpacing: 20,
            headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
            columns: const [
              DataColumn(
                label: Text(
                  'Created By',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Description',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Created At',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              DataColumn(
                label: Text(
                  'Updated At',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
            rows: widget.item.historyLogs!.map((log) {
              return DataRow(
                cells: [
                  DataCell(Text('User ${log.createdBy}')),
                  DataCell(Text(log.description)),
                  DataCell(Text(
                      log.createdAt.substring(0, 16).replaceAll('T', ' '))),
                  DataCell(Text(
                      log.updatedAt.substring(0, 16).replaceAll('T', ' '))),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
              label: const Text(
                'Approve',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showApprovalDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Đã duyệt yêu cầu thành công')),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(
                Icons.cancel,
                color: Colors.white,
                size: 24,
              ),
              label: const Text(
                'Reject',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                ApprovalService.showRejectDialog(
                  context: context,
                  itemId: widget.item.id,
                  onSuccess: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Đã từ chối yêu cầu thành công')),
                    );
                    Navigator.pop(context, true);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: isApprover(),
      builder: (context, snapshot) {
        final bool isApproverRole = snapshot.data ?? false;

        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: Text(
                'Invoice Details - ${widget.item.title}',
                style: const TextStyle(color: Colors.black),
              ),
              backgroundColor: Colors.white,
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.black),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.white,
              actions: [
                if (isApproverRole == false &&
                    ['draft', 'fail']
                        .contains(widget.item.status.toLowerCase()))
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.black),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => InvoiceForm(
                            editData: widget.item,
                            formTitle: 'Chỉnh sửa hóa đơn',
                          ),
                        ),
                      );
                    },
                  ),
              ],
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(1),
                child: Container(
                  color: Colors.grey.shade600,
                  height: 1,
                ),
              ),
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Invoice Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Title Field
                        _buildDetailField(
                          'Title',
                          widget.item.title,
                          Icons.title,
                        ),
                        const SizedBox(height: 16),

                        // Buyer Name Field
                        _buildDetailField(
                          'Buyer Name',
                          widget.item.buyerName,
                          Icons.person,
                        ),
                        const SizedBox(height: 16),

                        // Tax Code Field
                        _buildDetailField(
                          'Tax Code',
                          widget.item.taxCode,
                          Icons.qr_code,
                        ),
                        const SizedBox(height: 16),

                        // Currency Field
                        _buildDetailField(
                          'Currency',
                          widget.item.currency,
                          Icons.monetization_on,
                        ),
                        const SizedBox(height: 16),

                        // Quantity Field
                        _buildDetailField(
                          'Quantity',
                          widget.item.quantity?.toString() ?? 'N/A',
                          Icons.format_list_numbered,
                        ),
                        const SizedBox(height: 16),

                        // Unit Price Field
                        _buildAmountField(),
                        const SizedBox(height: 16),

                        // Total Price Field
                        if (widget.item.totalPrice != null) ...[
                          _buildTotalPriceField(),
                          const SizedBox(height: 16),
                        ],

                        // Swift Code Field (conditional)
                        if (widget.item.currency != 'VND' &&
                            widget.item.swiftCode != null) ...[
                          const SizedBox(height: 16),
                          _buildDetailField(
                            'Swift Code',
                            widget.item.swiftCode ?? '',
                            Icons.code,
                          ),
                        ],
                        const SizedBox(height: 16),

                        // Description Field
                        _buildDetailField(
                          'Description',
                          widget.item.description,
                          Icons.description,
                          maxLines: 3,
                        ),
                        // Attachments Section
                        if (widget.item.attachments != null &&
                            widget.item.attachments!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildAttachmentsSection(),
                        ],

                        // History Logs Section
                        if (widget.item.historyLogs != null &&
                            widget.item.historyLogs!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          _buildHistoryLogsSection(),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                if (isApproverRole &&
                    widget.item.status.toLowerCase() == 'pendingapprover') ...[
                  _buildActionButtons(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
