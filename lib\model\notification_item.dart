class NotificationItem {
  final int id;
  final int userId;
  final String title;
  final String body;
  final String type;
  final int refId;
  final bool isRead;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  NotificationItem({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.refId,
    required this.isRead,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    return NotificationItem(
      id: json['id'],
      userId: json['userId'],
      title: json['title'],
      body: json['body'],
      type: json['type'],
      refId: json['refId'],
      isRead: json['isRead'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
    );
  }

  // Ki<PERSON>m tra xem thông báo đã đọc chưa
  bool get read => isRead == 1;

  // Lấy định dạng ngày đẹp hơn
  String get formattedDate {
    final dateTime = DateTime.parse(createdAt);
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
