import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/page/pageLogin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

class pageChangePassword extends StatefulWidget {
  final String? serverOTP; // Thêm tham số để nhận OTP từ bên ngoài

  const pageChangePassword(
      {super.key, this.serverOTP = "123456" // Giá trị mặc định để test
      });

  @override
  State<pageChangePassword> createState() => _pageChangePasswordState();
}

class _pageChangePasswordState extends State<pageChangePassword> {
  bool isPasswordMatch = false;
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    // Add listeners to both controllers
    _newPasswordController.addListener(checkPasswordMatch);
    _confirmPasswordController.addListener(checkPasswordMatch);
  }

  // Function to check if passwords match
  void checkPasswordMatch() {
    setState(() {
      isPasswordMatch = _newPasswordController.text.isNotEmpty &&
          _newPasswordController.text.length >= 6 && // Check minimum length
          _confirmPasswordController.text.length >= 6 && // Check minimum length
          _newPasswordController.text == _confirmPasswordController.text;
    });
  }

  @override
  void dispose() {
    // Remove listeners and dispose controllers
    _newPasswordController.removeListener(checkPasswordMatch);
    _confirmPasswordController.removeListener(checkPasswordMatch);
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final minSize = min(screenWidth, screenHeight);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Đổi mật khẩu',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: minSize * 0.05),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(height: minSize * 0.02),
                  Container(
                    width: minSize * 0.85, // Cùng chiều rộng với TextField
                    alignment: Alignment.centerLeft, // Căn trái
                    child: Text(
                      'Mật khẩu mới',
                      style: TextStyle(
                        fontSize: minSize * 0.04,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.01),
                  SizedBox(
                    width: minSize * 0.85,
                    child: TextField(
                      controller: _newPasswordController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Nhập mật khẩu mới (ít nhất 6 ký tự)',
                        errorText: _newPasswordController.text.isNotEmpty &&
                                _newPasswordController.text.length < 6
                            ? 'Mật khẩu phải có ít nhất 6 ký tự'
                            : null,
                      ),
                      obscureText: true,
                    ),
                  ),
                  SizedBox(height: minSize * 0.04),
                  Container(
                    width: minSize * 0.85, // Cùng chiều rộng với TextField
                    alignment: Alignment.centerLeft, // Căn trái
                    child: Text(
                      'Xác nhận mật khẩu',
                      style: TextStyle(
                        fontSize: minSize * 0.04,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.01),
                  SizedBox(
                    width: minSize * 0.85,
                    child: TextField(
                      controller: _confirmPasswordController,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Nhập lại mật khẩu mới (ít nhất 6 ký tự)',
                        errorText: _confirmPasswordController.text.isNotEmpty &&
                                _confirmPasswordController.text.length < 6
                            ? 'Mật khẩu phải có ít nhất 6 ký tự'
                            : null,
                      ),
                      obscureText: true,
                    ),
                  ),
                  SizedBox(height: minSize * 0.04),
                  SizedBox(
                    width: minSize * 0.6,
                    height: minSize * 0.1,
                    child: ElevatedButton(
                      onPressed: () {
                        // Kiểm tra điều kiện mật khẩu
                        if (_newPasswordController.text.isEmpty ||
                            _newPasswordController.text.length < 6) {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Thông báo'),
                              content: const Text(
                                  'Mật khẩu mới phải có ít nhất 6 ký tự'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('OK'),
                                ),
                              ],
                            ),
                          );
                          return;
                        }

                        // Kiểm tra mật khẩu xác nhận
                        if (_confirmPasswordController.text.isEmpty ||
                            _confirmPasswordController.text.length < 6) {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Thông báo'),
                              content:
                                  const Text('Vui lòng xác nhận mật khẩu mới'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('OK'),
                                ),
                              ],
                            ),
                          );
                          return;
                        }

                        // Kiểm tra mật khẩu trùng khớp
                        if (_newPasswordController.text !=
                            _confirmPasswordController.text) {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Thông báo'),
                              content:
                                  const Text('Mật khẩu xác nhận không khớp'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('OK'),
                                ),
                              ],
                            ),
                          );
                          return;
                        }

                        // Nếu tất cả điều kiện đều thỏa mãn
                        Navigator.pushAndRemoveUntil(
                          context,
                          MaterialPageRoute(
                              builder: (context) => const pageLogin()),
                          (route) => false,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(minSize * 0.03),
                        ),
                      ),
                      child: Text(
                        'Tiếp theo',
                        style: TextStyle(
                          fontSize: minSize * 0.04,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: minSize * 0.02),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
