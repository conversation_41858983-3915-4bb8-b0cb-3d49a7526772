import 'package:flutter/material.dart';

class ActivityItem {
  final int id;
  final String title;
  final int categoryId;
  final String status;
  final int createdBy;
  final String bankNumber;
  final String accountHolder;
  final String bankName;
  final String currency;
  final String price;
  final int? quantity;
  final String? totalPrice;
  final String? swiftCode;
  final String description;
  final String buyerName;
  final String taxCode;
  final int taxTypeId;
  final String taxPeriod;
  final String? completedDate;
  final int? completedBy;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;
  final List<ActivityAttachment>? attachments;
  final Category category;
  final List<FormHistoryLog>? historyLogs; // Add this line

  ActivityItem({
    required this.id,
    required this.title,
    required this.categoryId,
    this.status = 'pending',
    required this.createdBy,
    required this.bankNumber,
    required this.accountHolder,
    required this.bankName,
    this.currency = 'VND',
    required this.price,
    this.quantity,
    this.totalPrice,
    this.swiftCode,
    required this.description,
    required this.buyerName,
    required this.taxCode,
    required this.taxTypeId,
    required this.taxPeriod,
    this.completedDate,
    this.completedBy,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.attachments = const [],
    required this.category,
    this.historyLogs = const [],
  });

  factory ActivityItem.fromJson(Map<String, dynamic> json) {
    // Xử lý price để bỏ phần thập phân
    String priceStr = (json['price'] ?? '0').toString();
    if (priceStr.contains('.')) {
      priceStr = priceStr.substring(0, priceStr.indexOf('.'));
    }

    return ActivityItem(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      categoryId: json['categoryId'] ?? 0,
      status: json['status'] ?? '', // Remove default 'pending' value
      createdBy: json['createdBy'] ?? 0,
      bankNumber: json['bankNumber'] ?? '',
      accountHolder: json['accountHolder'] ?? '',
      bankName: json['bankName'] ?? '',
      currency: json['currency'] ?? 'VND',
      price: priceStr,
      quantity: json['quantity'],
      totalPrice: json['totalPrice']?.toString(),
      swiftCode: json['swiftCode'],
      description: json['description'] ?? '',
      buyerName: json['buyerName'] ?? '',
      taxCode: json['taxCode'] ?? '',
      taxTypeId: json['taxTypeId'] ?? 1,
      taxPeriod: json['taxPeriod'] ?? '',
      completedDate: json['completedDate'],
      completedBy: json['completedBy'],
      createdAt: json['created_at'] ?? DateTime.now().toString(),
      updatedAt: json['updated_at'] ?? DateTime.now().toString(),
      deletedAt: json['deleted_at'],
      attachments: json['attachments'] == null
          ? []
          : List<ActivityAttachment>.from((json['attachments'] as List)
              .map((x) => ActivityAttachment.fromJson(x))),
      category: json['category'] == null
          ? Category(id: 0, name: 'Unknown')
          : Category.fromJson(json['category']),
      historyLogs: json['form_history_logs'] == null
          ? []
          : List<FormHistoryLog>.from((json['form_history_logs'] as List)
              .map((x) => FormHistoryLog.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'categoryId': categoryId,
      'status': status,
      'createdBy': createdBy,
      'bankNumber': bankNumber,
      'accountHolder': accountHolder,
      'bankName': bankName,
      'currency': currency,
      'price': price,
      'quantity': quantity,
      'totalPrice': totalPrice,
      'swiftCode': swiftCode,
      'description': description,
      'buyerName': buyerName,
      'taxCode': taxCode,
      'taxTypeId': taxTypeId,
      'taxPeriod': taxPeriod,
      'completedDate': completedDate,
      'completedBy': completedBy,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'attachments': attachments?.map((x) => x.toJson()).toList(),
      'category': category.toJson(),
      'form_history_logs': historyLogs?.map((x) => x.toJson()).toList(),
    };
  }
}

class ActivityAttachment {
  final int id;
  final String fileName;
  final String fileSize; // Thay đổi từ int sang String
  final String mimeType;
  final String url;

  ActivityAttachment({
    required this.id,
    required this.fileName,
    required this.fileSize,
    required this.mimeType,
    required this.url,
  });

  factory ActivityAttachment.fromJson(Map<String, dynamic> json) {
    return ActivityAttachment(
      id: json['id'] as int,
      fileName: json['fileName'] as String,
      fileSize: json['fileSize'] as String, // API trả về fileSize dạng String
      mimeType: json['mimeType'] as String,
      url: json['url'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileSize': fileSize,
      'mimeType': mimeType,
      'url': url,
    };
  }
}

class Category {
  final int id;
  final String name;

  Category({
    required this.id,
    required this.name,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

// Add new class for FormHistoryLog
class FormHistoryLog {
  final int id;
  final int formId;
  final int createdBy;
  final String description;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  FormHistoryLog({
    required this.id,
    required this.formId,
    required this.createdBy,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory FormHistoryLog.fromJson(Map<String, dynamic> json) {
    return FormHistoryLog(
      id: json['id'],
      formId: json['formId'],
      createdBy: json['createdBy'],
      description: json['description'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'formId': formId,
      'createdBy': createdBy,
      'description': description,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }
}

// Example widget to display category and date
class ActivityWidget extends StatelessWidget {
  final ActivityItem item;

  const ActivityWidget({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.category, size: 16),
            const SizedBox(width: 4),
            Text(
              'Category ${item.category.name}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8), // Thêm khoảng cách giữa category và date
        Text(
          'Created Date: ${item.createdAt}',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
