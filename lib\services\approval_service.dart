import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:du_an_flutter/APIconfig/api_config.dart';
import 'package:du_an_flutter/APIconfig/api_endpoints.dart';
import 'package:du_an_flutter/page/pageHome.dart';
import 'package:du_an_flutter/page/pageHomeApprover.dart';
import 'package:du_an_flutter/Screen/pendingApprover.dart';

class ApprovalService {
  // Danh sách các app ngân hàng phổ biến tại Việt Nam
  static const List<BankingApp> _bankingApps = [
    BankingApp(
      name: 'Vietcombank',
      packageName: 'com.VCB',
      displayName: 'VCB Digibank',
      playStoreUrl: 'https://play.google.com/store/apps/details?id=com.VCB',
      appStoreUrl: 'https://apps.apple.com/vn/app/vcb-digibank/id561433133',
    ),
    BankingApp(
      name: 'Techcombank',
      packageName: 'com.techcombank.bb.app',
      displayName: 'Techcombank Mobile',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=com.techcombank.bb.app',
      appStoreUrl:
          'https://apps.apple.com/vn/app/techcombank-mobile/id1157642538',
    ),
    BankingApp(
      name: 'BIDV',
      packageName: 'bidv.smartbanking',
      displayName: 'BIDV Smart Banking',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=bidv.smartbanking',
      appStoreUrl:
          'https://apps.apple.com/vn/app/bidv-smart-banking/id1547020350',
    ),
    BankingApp(
      name: 'VietinBank',
      packageName: 'com.vietinbank.ipay',
      displayName: 'VietinBank iPay',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=com.vietinbank.ipay',
      appStoreUrl: 'https://apps.apple.com/vn/app/vietinbank-ipay/id1412546884',
    ),
    BankingApp(
      name: 'Agribank',
      packageName: 'vn.com.agribank.mobilebanking',
      displayName: 'Agribank E-Mobile Banking',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=vn.com.agribank.mobilebanking',
      appStoreUrl:
          'https://apps.apple.com/vn/app/agribank-e-mobile-banking/id1235644741',
    ),
    BankingApp(
      name: 'MBBank',
      packageName: 'com.mbmobile',
      displayName: 'MB Bank',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=com.mbmobile',
      appStoreUrl: 'https://apps.apple.com/vn/app/mbbank/id1273702831',
    ),
    BankingApp(
      name: 'ACB',
      packageName: 'mobile.acb.com.vn',
      displayName: 'ACB ONE',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=mobile.acb.com.vn',
      appStoreUrl: 'https://apps.apple.com/vn/app/acb-one/id1346821193',
    ),
    BankingApp(
      name: 'TPBank',
      packageName: 'com.tpb.mb.gprsandroid',
      displayName: 'TPBank Mobile',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=com.tpb.mb.gprsandroid',
      appStoreUrl: 'https://apps.apple.com/vn/app/tpbank-mobile/id1100950702',
    ),
    BankingApp(
      name: 'VPBank',
      packageName: 'com.vnpay.vpbankonline',
      displayName: 'VPBank NEO',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=com.vnpay.vpbankonline',
      appStoreUrl: 'https://apps.apple.com/vn/app/vpbank-neo/id1485488659',
    ),
    BankingApp(
      name: 'SHB',
      packageName: 'vn.shb.mbanking',
      displayName: 'SHB Mobile',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=vn.shb.mbanking',
      appStoreUrl: 'https://apps.apple.com/vn/app/shb-mobile/id1476208625',
    ),
    BankingApp(
      name: 'Sacombank',
      packageName: 'sacombank.com.vn',
      displayName: 'Sacombank mBanking',
      playStoreUrl:
          'https://play.google.com/store/apps/details?id=sacombank.com.vn',
      appStoreUrl:
          'https://apps.apple.com/vn/app/sacombank-mbanking/id1570783779',
    ),
    BankingApp(
      name: 'HDBank',
      packageName: 'com.hdbank',
      displayName: 'HDBank',
      playStoreUrl: 'https://play.google.com/store/apps/details?id=com.hdbank',
      appStoreUrl: 'https://apps.apple.com/vn/app/hdbank/id1563576725',
    ),
  ];

  static Future<void> _showBankingAppsDialog({
    required BuildContext context,
    int? itemId,
    VoidCallback? onComplete,
  }) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) => _BankingAppsDialog(
        itemId: itemId,
        onComplete: onComplete,
      ),
    );
  }

  static Future<bool> approveItem(int itemId, BuildContext context,
      {VoidCallback? onComplete}) async {
    try {
      print('Approving item $itemId');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Request approved successfully')),
        );

        // Hiển thị danh sách app ngân hàng sau khi approve thành công
        if (context.mounted) {
          await _showBankingAppsDialog(
            context: context,
            itemId: itemId,
            onComplete: onComplete,
          );
        }
      }
      return true;
    } catch (e) {
      print('Error approving item: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error approving: $e')),
        );
      }
      return false;
    }
  }

  static Future<bool> rejectItem(int itemId, BuildContext context,
      {String? reason, VoidCallback? onComplete}) async {
    try {
      print(
          'Rejecting item $itemId with reason: ${reason ?? "No reason provided"}');

      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Authentication token not found');
      }

      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.updateFormApprove}/$itemId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'status': 'fail',
          'comment': reason ?? 'No reason provided',
        },
      );

      print('Reject API Response Status: ${response.statusCode}');
      print('Reject API Response Data: ${response.data}');

      if (response.statusCode == 200) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Request rejected successfully'),
              backgroundColor: Colors.orange,
            ),
          );
        }

        // Trigger UI refresh after successful rejection
        onComplete?.call();

        // Trigger home screen refresh for both regular and approver pages
        try {
          await HomePage.triggerHomeRefresh();
        } catch (e) {
          print('Error triggering HomePage refresh: $e');
        }

        try {
          await HomePageApprover.triggerHomeRefresh();
        } catch (e) {
          print('Error triggering HomePageApprover refresh: $e');
        }

        // Trigger PendingApproverScreen refresh
        try {
          await PendingApproverScreenState.triggerRefresh();
        } catch (e) {
          print('Error triggering PendingApproverScreen refresh: $e');
        }

        return true;
      } else {
        throw Exception('Failed to reject item: ${response.statusCode}');
      }
    } catch (e) {
      print('Error rejecting item: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rejecting: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  static Future<void> showApprovalDialog({
    required BuildContext context,
    required int itemId,
    required VoidCallback onSuccess,
  }) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Confirm'),
        content: const Text('Are you sure you want to approve this request?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(dialogContext);
              final success =
                  await approveItem(itemId, context, onComplete: onSuccess);
              // Note: onSuccess is now called from within the banking app dialog
              // after the countdown completes and API call succeeds
            },
            style: TextButton.styleFrom(foregroundColor: Colors.green),
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  static Future<void> showRejectDialog({
    required BuildContext context,
    required int itemId,
    required VoidCallback onSuccess,
  }) async {
    if (!context.mounted) return;

    return showDialog<void>(
      barrierDismissible: true,
      context: context,
      builder: (BuildContext dialogContext) {
        final TextEditingController reasonController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setState) {
            return WillPopScope(
              onWillPop: () async {
                reasonController.dispose();
                return true;
              },
              child: AlertDialog(
                title: const Text('Reject Request'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text('Bạn có chắc chắn muốn reject request này?'),
                    const SizedBox(height: 16),
                    TextField(
                      controller: reasonController,
                      decoration: const InputDecoration(
                        labelText: 'Lý do reject',
                        hintText: 'Nhập lý do reject',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        setState(() {});
                      },
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      FocusScope.of(dialogContext).unfocus();
                      reasonController.dispose();
                      if (Navigator.of(dialogContext).canPop()) {
                        Navigator.of(dialogContext).pop();
                      }
                    },
                    child: const Text('Hủy'),
                  ),
                  ElevatedButton(
                    onPressed: reasonController.text.trim().isEmpty
                        ? null
                        : () async {
                            final reason = reasonController.text.trim();
                            FocusScope.of(dialogContext).unfocus();
                            reasonController.dispose();
                            if (Navigator.of(dialogContext).canPop()) {
                              Navigator.of(dialogContext).pop();
                            }
                            if (context.mounted) {
                              final success = await rejectItem(
                                itemId,
                                context,
                                reason: reason,
                                onComplete: onSuccess,
                              );
                              // Note: onSuccess is now called from within rejectItem
                              // after the API call succeeds
                            }
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      disabledBackgroundColor: Colors.grey,
                    ),
                    child: const Text('Reject'),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

class BankingApp {
  final String name;
  final String packageName;
  final String displayName;
  final String playStoreUrl;
  final String appStoreUrl;

  const BankingApp({
    required this.name,
    required this.packageName,
    required this.displayName,
    required this.playStoreUrl,
    required this.appStoreUrl,
  });
}

class _BankingAppsDialog extends StatefulWidget {
  final int? itemId;
  final VoidCallback? onComplete;

  const _BankingAppsDialog({
    this.itemId,
    this.onComplete,
  });

  @override
  State<_BankingAppsDialog> createState() => _BankingAppsDialogState();
}

class _BankingAppsDialogState extends State<_BankingAppsDialog> {
  Timer? _countdownTimer;
  int _countdownSeconds = 10;
  bool _isCountdownComplete = false;
  bool _isApiLoading = false;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
      } else {
        setState(() {
          _isCountdownComplete = true;
        });
        timer.cancel();
      }
    });
  }

  Future<void> _openBankingApp(BankingApp app) async {
    try {
      // Thử mở app trực tiếp
      String appUrl;

      if (Platform.isAndroid) {
        // Thử mở app bằng package name
        appUrl = 'market://launch?id=${app.packageName}';

        if (await canLaunchUrl(Uri.parse(appUrl))) {
          await launchUrl(Uri.parse(appUrl));
        } else {
          // Nếu không mở được, chuyển đến Play Store
          await launchUrl(
            Uri.parse(app.playStoreUrl),
            mode: LaunchMode.externalApplication,
          );
        }
      } else if (Platform.isIOS) {
        // Đối với iOS, chuyển đến App Store
        await launchUrl(
          Uri.parse(app.appStoreUrl),
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      print('Error opening app: $e');
      // Fallback: mở trình duyệt với link tải app
      String fallbackUrl =
          Platform.isAndroid ? app.playStoreUrl : app.appStoreUrl;
      await launchUrl(
        Uri.parse(fallbackUrl),
        mode: LaunchMode.externalApplication,
      );
    }
  }

  Future<void> _completeProcess() async {
    if (widget.itemId == null) {
      Navigator.of(context).pop();
      widget.onComplete?.call();
      return;
    }

    setState(() {
      _isApiLoading = true;
    });

    try {
      final dio = Dio();
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      final response = await dio.post(
        '${ApiConfig.baseUrl}${ApiEndpoints.updateFormApprove}/${widget.itemId}',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'status': 'approved',
        },
      );

      if (response.statusCode == 200 && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Banking app selection completed successfully'),
          ),
        );
        Navigator.of(context).pop();
        widget.onComplete?.call();

        // Trigger home screen refresh for both regular and approver pages
        try {
          await HomePage.triggerHomeRefresh();
        } catch (e) {
          print('Error triggering HomePage refresh: $e');
        }

        try {
          await HomePageApprover.triggerHomeRefresh();
        } catch (e) {
          print('Error triggering HomePageApprover refresh: $e');
        }

        // Trigger PendingApproverScreen refresh
        try {
          await PendingApproverScreenState.triggerRefresh();
        } catch (e) {
          print('Error triggering PendingApproverScreen refresh: $e');
        }
      }
    } catch (e) {
      print('Error completing process: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isApiLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Banking App'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select a banking app to open or download:',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 16),
              ...ApprovalService._bankingApps.map((app) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.account_balance,
                          color: Colors.blue,
                          size: 24,
                        ),
                      ),
                      title: Text(
                        app.displayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      subtitle: Text(
                        app.name,
                        style: const TextStyle(fontSize: 12),
                      ),
                      trailing: const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey,
                      ),
                      onTap: () {
                        _openBankingApp(app);
                      },
                    ),
                  )),
            ],
          ),
        ),
      ),
      actions: [
        // Close button with countdown or Complete button
        if (!_isCountdownComplete)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close ($_countdownSeconds)'),
          )
        else
          TextButton(
            onPressed: _isApiLoading ? null : _completeProcess,
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
            child: _isApiLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Hoàn thành'), // Vietnamese for "Complete"
          ),
      ],
    );
  }
}
